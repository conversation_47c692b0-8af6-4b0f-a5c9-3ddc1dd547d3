{"name": "cc-front", "version": "0.0.0", "private": true, "type": "module", "scripts": {"dev": "vite --host", "build": "vite build", "preview": "vite preview", "pub": "vite build && scp -r dist/ ccyun:/root/sites/ak/front", "lint": "eslint . --fix", "format": "prettier --write src/"}, "dependencies": {"@popperjs/core": "^2.11.8", "@tailwindcss/vite": "^4.0.6", "@vueuse/core": "^13.3.0", "graphql": "^16.10.0", "graphql-request": "^7.1.2", "graphql-tag": "^2.12.6", "v-calendar": "^3.1.2", "vue": "^3.5.13", "vue-router": "^4.5.0", "vuedraggable": "^4.1.0"}, "devDependencies": {"@arco-design/web-vue": "^2.57.0", "@arco-plugins/vite-vue": "^1.4.5", "@eslint/js": "^9.18.0", "@vitejs/plugin-vue": "^5.2.1", "@vue/eslint-config-prettier": "^10.1.0", "eslint": "^9.18.0", "eslint-plugin-vue": "^9.32.0", "prettier": "^3.4.2", "sass-embedded": "^1.83.4", "tailwindcss": "^4.0.6", "vite": "^6.0.11", "vite-plugin-vue-devtools": "^7.7.1"}}