<template>
  <div v-if="error">
    <a-result title="出错了!" :subtitle="error.message" status="500">
      <template #extra>
        <a-button type="primary" @click="reload">刷新</a-button>
      </template>
    </a-result>
  </div>
  <div v-else-if="!loaded" class="w-full">
    <a-skeleton animation>
      <div class="flex gap-4 pb-4">
        <a-skeleton-shape />
        <div class="flex-1">
          <a-skeleton-line :rows="2" />
        </div>
      </div>
      <a-skeleton-line :rows="5" />
      <div class="flex gap-4 pt-4">
        <a-skeleton-shape />
        <div class="flex-1">
          <a-skeleton-line :rows="2" />
        </div>
      </div>
    </a-skeleton>
  </div>
  <slot v-else></slot>
</template>
<script setup>
defineProps({
  error: {
    type: Boolean,
    default: false,
  },
  loaded: {
    type: Boolean,
    default: false,
  },
})
function reload() {
  window.location.reload()
}
</script>
