<script setup>
import { computed } from 'vue'
const props = defineProps({
  page: Number,
  size: Number,
  total: Number,
  goPage: Function,
})
function goToPage(page) {
  props.goPage(page, props.size)
}
const noMore = computed(() => props.page * props.size >= props.total)
function loadMore() {
  if (noMore.value) return
  props.goPage(props.page + 1, props.size, true)
}
</script>

<template>
  <div v-if="total === 0" class="text-center text-muted-color text-sm mt-4"></div>
  <div v-else>
    <div class="py-4 hidden md:block">
      <a-pagination
        :total="total"
        :page-size="size"
        :page-size-options="[20, 40, 60]"
        @change="goToPage"
        @page-size-change="(size) => goToPage(1, size)"
      ></a-pagination>
    </div>
    <div class="block md:hidden text-center py-4" v-scroll-fire:multiple="loadMore">
      {{ noMore ? '没有更多了' : '加载中...' }}
    </div>
  </div>
</template>
