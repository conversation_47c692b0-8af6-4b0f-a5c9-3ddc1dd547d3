<template>
  <div class="space-y-2">
    <div
      v-for="rank in list"
      :key="rank.id"
      class="flex items-center p-2 rounded-md hover:bg-[var(--color-fill-2)]"
    >
      <!-- Rank Number -->
      <div class="w-8 text-center font-medium">
        {{ rank.rank }}
      </div>

      <!-- User Avatar -->
      <div class="mx-2 flex-1">
        <user-avatar :user="rank.user" />
      </div>

      <!-- Points -->
      <div class="text-lg font-medium text-[var(--color-text-1)]">
        {{ rank.points }}
      </div>
    </div>

    <!-- Empty State -->
    <div v-if="!list.length" class="text-center py-8 text-[var(--color-text-3)]">暂无排行数据</div>
  </div>
</template>

<script setup>
import UserAvatar from './UserAvatar.vue'

defineProps({
  list: {
    type: Array,
    default: () => [],
  },
})
</script>
