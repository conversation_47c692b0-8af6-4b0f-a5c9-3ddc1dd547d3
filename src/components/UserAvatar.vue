<template>
  <div class="cursor-pointer flex items-center gap-x-2" @click="showDrawer" v-bind="$attrs">
    <a-avatar :size="35">
      <img v-if="u.avatar" :src="u.avatar + '-avatar.jpg'" />
      <IconUser v-else />
    </a-avatar>
    <div class="flex-1">
      <div class="font-bold text-sm overflow-hidden text-ellipsis whitespace-nowrap">
        {{ u.name }}
      </div>
      <div :class="{ 'text-xs': u.name, 'font-bold': !u.name }" class="flex items-center">
        <a-tooltip v-if="u.markName" content="备注名" position="top" mini>
          <div class="overflow-hidden text-ellipsis whitespace-nowrap">
            <span>{{ u.markName }}</span>
            <span class="text-[var(--color-text-2)] ml-0.5">
              <IconInfoCircle />
            </span>
          </div>
        </a-tooltip>
      </div>
      <div
        class="text-[var(--color-text-2)] flex"
        :class="{ 'font-bold': !u.name && !u.markName, 'text-xs': u.name || u.markName }"
      >
        {{ u.mobile }}
      </div>
      <div class="">
        <a-tag
          v-if="!u.registered"
          color="orange"
          size="small"
          style="font-size: 10px; line-height: 16px; height: 16px; padding: 0 3px"
          >未注册
        </a-tag>
        <a-tag
          v-else-if="!u.profileCompleted"
          color="orange"
          size="small"
          style="font-size: 10px; line-height: 16px; height: 16px; padding: 0 3px"
          >资料未完善
        </a-tag>
      </div>
    </div>
    <div class="text-right" v-if="showPoints">
      <div>
        <span class="font-semibold text-sm text-[rgb(var(--primary-6))]">{{
          u.currentPoints
        }}</span>
        <span class="text-[var(--color-text-3)]"
          ><icon-oblique-line class="text-[var(--color-text-3)]"
        /></span>

        <span class="font-semibold text-sm text-[rgb(var(--primary-6))]">{{
          u.accumulatedPoints
        }}</span>
        <span class="text-xs text-[var(--color-text-2)] ml-1">积分</span>
      </div>
      <div v-if="showAddition" class="text-xs">
        <div>
          <GenderLabel :gender="u.gender" />
          <a-divider direction="vertical" />
          <span class="font-semibold mr-0.5">{{ u.level }}</span
          >档
        </div>
      </div>
    </div>
  </div>
  <div v-if="showAddition" class="mt-2 flex-1 flex flex-col">
    <!-- <div style="margin-left: 35px" class="pl-2 flex-1">
      <div class="text-sm mb-1">
        <div class="flex gap-x-2">
          <GenderLabel :gender="u.gender" />
          <date-time :time="u.birthday" v-if="u.birthday" format="YY-MM-DD"></date-time>
          <span v-else class="">生日未完善</span>
          <div>{{ u.level }}档</div>
        </div>
      </div>
    </div> -->
    <div class="flex justify-between border-t border-[var(--color-border-1)] pt-1 pb-1">
      <a-button type="text" @click="openEditDrawer" size="small">
        <template #icon><IconEdit /></template>
        编辑
      </a-button>
      <a-button type="text" @click="openLockerDrawer" size="small">
        <template #icon><IconCommon /></template>
        球柜
      </a-button>
      <a-button type="text" @click="openKbReqDrawer" size="small" v-permission="['operator']">
        <template #icon><IconPlus /></template>
        K币
      </a-button>
      <a-button
        type="text"
        @click="openKbConsumeDrawer"
        size="small"
        v-permission="['admin', 'executor']"
      >
        <template #icon><IconMinus /></template>
        K币
      </a-button>
      <a-button
        type="text"
        @click="openPointsDrawer"
        size="small"
        v-permission="['admin', 'executor']"
      >
        <template #icon><IconMinus /></template>
        积分
      </a-button>
      <!-- <a-button type="text" @click="openPointsDrawer('add')" size="small">
        <template #icon><IconPlus /></template>
        能量
      </a-button>
      <a-button type="text" @click="openPointsDrawer('consume')" size="small">
        <template #icon><IconMinus /></template>
        能量
      </a-button> -->
    </div>
  </div>
  <a-drawer
    unmount-on-close
    v-model:visible="drawerVisible"
    :title="u.name ?? u.markName ?? u.mobile"
    :placement="isMdScreen ? 'right' : 'bottom'"
    :width="500"
    height="90%"
    :footer="false"
  >
    <div class="flex items-center gap-x-2 mb-2">
      <div>
        <a-avatar :size="55">
          <img v-if="u.avatar" :src="u.avatar + '-avatar.jpg'" />
          <IconUser v-else />
        </a-avatar>
      </div>
      <div class="flex-1">
        <div class="font-bold text-sm flex">
          <div v-if="u.name">{{ u.name }}</div>
          <a-tooltip v-if="u.markName" content="备注名" position="top" mini>
            <div class="flex items-center">
              <span class="text-[var(--color-text-3)]" v-if="u.name">（</span>
              <span>{{ u.markName }}</span>
              <span class="text-[var(--color-text-2)]">
                <IconInfoCircle />
              </span>
              <span class="text-[var(--color-text-3)]" v-if="u.name">）</span>
            </div>
          </a-tooltip>
        </div>
        <div class="flex items-center">
          <IconPhone />
          <a
            :href="`tel:${u.mobile}`"
            class="ml-1 cursor-pointer text-base"
            :class="{ 'font-bold': !u.name && !u.markName }"
            >{{ u.mobile }}</a
          >
        </div>
        <div class="">
          <a-tag
            v-if="!u.registered"
            color="orange"
            size="small"
            style="font-size: 10px; line-height: 16px; height: 16px; padding: 0 3px"
            >未注册
          </a-tag>
          <a-tag
            v-else-if="!u.profileCompleted"
            color="orange"
            size="small"
            style="font-size: 10px; line-height: 16px; height: 16px; padding: 0 3px"
            >资料未完善
          </a-tag>
        </div>
        <div v-if="u.registered" class="text-xs text-[var(--color-text-3)]" style="font-size: 10px">
          <span>注册：</span><date-time show-time :time="u.registeredAt" />
        </div>
      </div>
      <div>
        <div>
          <span class="font-semibold text-base text-[rgb(var(--primary-6))]">{{
            u.currentPoints
          }}</span>
          <span class="text-[var(--color-text-3)]"
            ><icon-oblique-line class="text-[var(--color-text-3)]"
          /></span>

          <span class="font-semibold text-base text-[rgb(var(--primary-6))]">{{
            u.accumulatedPoints
          }}</span>
          <span class="text-xs text-[var(--color-text-2)] ml-1">积分</span>
        </div>
      </div>
    </div>
    <div class="">
      <a-tabs v-model:active-tab="tab" lazy-load @change="changeTab">
        <template #extra>
          <a-dropdown>
            <a-button type="primary" size="mini"
              >操作<template #icon><icon-down /></template
            ></a-button>
            <template #content>
              <a-doption @click="openEditDrawer"
                ><IconEdit /><span class="ml-1">编辑信息</span></a-doption
              >
              <a-doption @click="openLockerDrawer"
                ><IconCommon /><span class="ml-1">球柜分配</span></a-doption
              >
              <a-doption @click="openKbReqDrawer"
                ><IconPlus /><span class="ml-1">提交K币</span></a-doption
              >
              <a-doption @click="openKbConsumeDrawer" v-permission="['admin', 'executor']"
                ><IconMinus /><span class="ml-1">消费K币</span></a-doption
              >
              <a-doption @click="openPointsDrawer" v-permission="['admin', 'executor']"
                ><IconMinus /><span class="ml-1">消费积分</span></a-doption
              >
            </template>
          </a-dropdown>
        </template>
        <a-tab-pane key="info" title="信息">
          <div>
            <a-descriptions :data="descData" :column="{ xs: 2, md: 4 }" />
          </div>
          <div class="mb-6">
            <a-divider orientation="left">K币</a-divider>
            <div v-if="u.userKbs?.length">
              <div class="flex flex-wrap gap-4">
                <a-tag v-for="uk in u.userKbs" :key="uk.id" class="flex">
                  <div>{{ uk.hall.name }}</div>
                  <div class="text-[var(--color-text-3)] mx-1">:</div>
                  <div class="font-bold">{{ uk.kb }}</div>
                  <div class="ml-3 text-[var(--color-text-2)]">排名：</div>
                  <div class="font-bold">{{ uk.rank }}</div>
                </a-tag>
              </div>
            </div>
            <div v-else class="text-[var(--color-text-3)] text-sm">没有K币</div>
          </div>
          <div class="mb-6">
            <a-divider orientation="left">积分</a-divider>
            <a-tag>
              <div class="">累计：</div>
              <div class="font-bold">{{ u.accumulatedPoints }}</div>
              <div class="ml-1 text-[var(--color-text-2)]">分</div>
              <div class="ml-3 text-[var(--color-text-2)]">排名：</div>
              <div class="font-bold">{{ u.accumulatedPointsRank }}</div>
            </a-tag>
            <a-tag class="ml-4">
              <div class="">当前：</div>
              <div class="font-bold">{{ u.currentPoints }}</div>
              <div class="ml-1 text-[var(--color-text-2)]">分</div>
            </a-tag>
          </div>

          <!-- 展示球柜 -->
          <a-divider orientation="left">球柜</a-divider>
          <div v-if="u.userLockers?.length" class="mb-1">
            <div class="flex flex-wrap gap-4">
              <a-tag v-for="ul in u.userLockers" :key="ul.id" class="flex">
                <div>{{ ul.locker.hall.name }}</div>
                <div class="text-[var(--color-text-3)] mx-1">:</div>
                <div class="font-bold">{{ ul.locker.lockerNo }}</div>
              </a-tag>
            </div>
          </div>
          <div v-else class="text-[var(--color-text-3)] text-sm">没有球柜</div>
        </a-tab-pane>
        <a-tab-pane key="kb" title="K币">
          <a-result
            v-if="kbLogsError"
            status="500"
            title="出错了"
            :sub-title="kbLogsError.message"
          ></a-result>
          <template v-else>
            <a-table :data="kbLogsList" :pagination="false" :bordered="false">
              <template #columns>
                <a-table-column title="K币" data-index="kb">
                  <template #cell="{ record }">
                    <div :class="record.kb > 0 ? 'text-green-500' : 'text-red-500'">
                      {{ record.kb > 0 ? '+' : '' }}{{ record.kb }}
                    </div>
                  </template>
                </a-table-column>
                <a-table-column title="门店" data-index="hall">
                  <template #cell="{ record }">
                    <a-tag size="mini">{{ record.hall.name }}</a-tag>
                  </template>
                </a-table-column>
                <a-table-column title="途径 理由" data-index="points">
                  <template #cell="{ record }">
                    <a-tag size="mini">{{ record.source }}</a-tag>
                    <div class="text-xs text-[var(--color-text-2)] mt-1">
                      {{ record.reason || '' }}
                    </div>
                  </template>
                </a-table-column>
                <a-table-column title="操作" data-index="createdAt" header-cell-class="hd-cell">
                  <template #title>
                    <div class="flex">
                      <div class="flex-1">操作</div>
                      <a-button
                        size="mini"
                        type="primary"
                        @click="searchKbLogs"
                        shape="circle"
                        :loading="kbLogsFetching"
                        ><template #icon><IconRefresh></IconRefresh></template
                      ></a-button>
                    </div>
                  </template>
                  <template #cell="{ record }">
                    <div class="text-xs text-[var(--color-text-2)]">
                      <div>{{ record.operator?.name }}</div>
                      <date-time
                        :time="record.createdAt"
                        format="YYYY-MM-DD HH:mm:ss"
                        class=""
                      ></date-time>
                    </div>
                  </template>
                </a-table-column>
              </template>
            </a-table>
            <pagination-bar
              :go-page="kbLogsGoPage"
              :page="kbLogsVariables.page"
              :size="kbLogsVariables.size"
              :total="kbLogsTotal"
            ></pagination-bar>
          </template>
        </a-tab-pane>
        <a-tab-pane key="points" title="积分">
          <a-result
            v-if="pointLogsError"
            status="500"
            title="出错了"
            :sub-title="pointLogsError.message"
          ></a-result>
          <template v-else>
            <a-table :data="pointLogsList" :pagination="false" :bordered="false">
              <template #columns>
                <a-table-column title="积分" data-index="points">
                  <template #cell="{ record }">
                    <div :class="record.points > 0 ? 'text-green-500' : 'text-red-500'">
                      {{ record.points > 0 ? '+' : '' }}{{ record.points }}
                    </div>
                  </template>
                </a-table-column>
                <a-table-column title="途径|来源" data-index="source">
                  <template #cell="{ record }">
                    <a-tag size="mini">{{ record.source }}</a-tag>
                    <div class="text-xs text-[var(--color-text-2)] mt-1">
                      {{ record.reason || '' }}
                    </div>
                  </template>
                </a-table-column>
                <a-table-column data-index="createdAt" header-cell-class="hd-cell">
                  <template #title>
                    <div class="flex">
                      <div class="flex-1">操作</div>
                      <a-button
                        size="mini"
                        type="primary"
                        @click="searchPointLogs"
                        shape="circle"
                        :loading="pointLogsFetching"
                        ><template #icon><IconRefresh></IconRefresh></template
                      ></a-button>
                    </div>
                  </template>
                  <template #cell="{ record }">
                    <div class="text-xs">{{ record.operator?.name }}</div>
                    <date-time
                      :time="record.createdAt"
                      format="YYYY-MM-DD HH:mm:ss"
                      class="text-xs text-[var(--color-text-2)]"
                    ></date-time>
                  </template>
                </a-table-column>
              </template>
            </a-table>
            <pagination-bar
              :go-page="pointLogsGoPage"
              :page="pointLogsVariables.page"
              :size="pointLogsVariables.size"
              :total="pointLogsTotal"
            ></pagination-bar>
          </template>
        </a-tab-pane>
        <a-tab-pane key="checkin" title="签到">
          <VCalendar
            :first-day-of-week="2"
            expanded
            :attributes="attributes"
            :max-date="new Date()"
            @did-move="moveCalendar"
          >
          </VCalendar>
          <div v-if="halls" class="flex gap-x-4 justify-end text-xs mt-2">
            <div v-for="h in halls" :key="h.id" class="flex items-center gap-x-1">
              <div
                :style="{ 'background-color': h.badgeColor }"
                style="height: 10px; width: 10px"
                class="rounded-full"
              ></div>
              <div>{{ h.name }}</div>
            </div>
          </div>
          <div v-if="checkinList" class="text-right text-[var(--color-text-2)] mt-1 text-xs">
            该月签到总次数：{{ checkinList.total }}
          </div>
        </a-tab-pane>
      </a-tabs>
    </div>
  </a-drawer>
  <!-- 编辑 drawer -->
  <a-drawer
    unmount-on-close
    v-model:visible="editDrawerVisible"
    title="编辑用户"
    :footer="false"
    :width="500"
    :placement="isMdScreen ? 'right' : 'bottom'"
    height="90%"
  >
    <a-form :model="userForm" @submit="userSubmit" ref="drawerFormRef" layout="horizontal">
      <a-form-item field="markName" label="备注名">
        <a-input v-model="userForm.markName" placeholder="请输入备注名" allow-clear />
      </a-form-item>
      <a-form-item field="gender" label="性别" v-if="!user.profileCompleted">
        <a-radio-group v-model="userForm.gender">
          <a-radio :value="1">男</a-radio>
          <a-radio :value="2">女</a-radio>
          <a-radio :value="0">未知</a-radio>
        </a-radio-group>
      </a-form-item>
      <a-form-item field="level" label="档位">
        <a-select v-model="userForm.level">
          <a-option :value="(i + 1) / 2" v-for="i in 19" :key="i"> {{ (i + 1) / 2 }}档</a-option>
        </a-select>
      </a-form-item>
      <a-form-item>
        <a-button html-type="submit" type="primary" long>保存</a-button>
      </a-form-item>
    </a-form>
  </a-drawer>
  <!-- 球柜 drawer -->
  <a-drawer
    unmount-on-close
    v-model:visible="lockerDrawerVisible"
    :title="`分配球柜（${u.name || u.markName || u.mobile}）`"
    :width="500"
    :placement="isMdScreen ? 'right' : 'bottom'"
    height="90%"
    :footer="false"
  >
    <div>
      <div class="mb-4">
        <a-divider orientation="left">当前分配的球柜</a-divider>
        <div v-if="u.userLockers?.length" class="space-y-2">
          <div
            v-for="ul in u.userLockers"
            :key="ul.id"
            class="flex items-center justify-between bg-[var(--color-bg-2)] p-2 rounded"
          >
            <div>
              <div>{{ ul.locker.hall.name }} - {{ ul.locker.lockerNo }}</div>
              <div v-if="ul.locker.remark" class="text-[var(--color-text-3)] text-sm">
                {{ ul.locker.remark }}
              </div>
            </div>
            <a-popconfirm content="确定解除该球柜分配吗？" @ok="handleUnassignLocker(ul)">
              <a-button type="text" status="danger">
                <template #icon><IconDelete /></template>
              </a-button>
            </a-popconfirm>
          </div>
        </div>
        <div v-else class="text-[var(--color-text-3)]">暂无分配的球柜</div>
      </div>

      <div class="mb-4">
        <a-divider orientation="left">分配新球柜</a-divider>
        <a-form layout="horizontal" :model="{}">
          <a-form-item field="hall" label="选择场馆">
            <a-select
              v-model="selectedHall"
              placeholder="请选择场馆"
              @change="selectedLocker = null"
            >
              <a-option
                v-for="(hall, index) in account.halls || halls"
                :key="hall.id"
                :value="index"
              >
                {{ hall.name }}
              </a-option>
            </a-select>
          </a-form-item>
          <a-form-item field="locker" label="选择球柜">
            <a-select v-model="selectedLocker" placeholder="请选择球柜" allow-search>
              <template v-for="locker in halls[selectedHall]?.lockers" :key="locker.id">
                <a-option
                  :value="locker.id"
                  :disabled="locker.status !== 1"
                  v-if="!locker.userLocker"
                >
                  {{ locker.lockerNo }}
                  <template #extra>
                    <span v-if="locker.remark" class="text-[var(--color-text-3)] text-sm">
                      ({{ locker.remark }})
                    </span>
                    <span v-if="locker.status !== 1" class="text-[var(--color-text-3)] text-sm">
                      (不可用)
                    </span>
                  </template>
                </a-option>
              </template>
            </a-select>
          </a-form-item>
          <a-form-item>
            <a-button type="primary" long @click="handleAssignLocker" :disabled="!selectedLocker">
              分配
            </a-button>
          </a-form-item>
        </a-form>
      </div>
    </div>
  </a-drawer>
  <!-- kb 添加请求 drawer -->
  <a-drawer
    unmount-on-close
    v-model:visible="kbReqDrawerVisible"
    :title="`+ K币 提交（${u.name || u.markName || u.mobile})`"
    :width="500"
    :placement="isMdScreen ? 'right' : 'bottom'"
    height="90%"
    :footer="false"
  >
    <div>
      <a-form :model="kbReqForm" layout="horizontal" @submit="handleKbReqSubmit">
        <a-form-item
          field="hallId"
          label="门店"
          :rules="[{ required: true, message: '请选择门店' }]"
        >
          <a-select v-model="kbReqForm.hallId" placeholder="请选择门店">
            <a-option v-for="hall in account.halls || halls" :key="hall.id" :value="hall.id">
              {{ hall.name }}
            </a-option>
          </a-select>
        </a-form-item>
        <a-form-item
          field="kb"
          label="K币"
          :rules="[
            { required: true, message: '请输入K币值' },
            { type: 'number', min: 1, message: 'K币值必须大于0' },
          ]"
        >
          <a-input-number
            v-model="kbReqForm.kb"
            placeholder="请输入K币值"
            :min="1"
            style="width: 100%"
          />
        </a-form-item>
        <a-form-item
          field="source"
          label="来源"
          :rules="[{ required: true, message: '请选择来源' }]"
        >
          <a-select v-model="kbReqForm.source" placeholder="请选择">
            <a-option value="消费奖励">消费奖励</a-option>
            <a-option value="比赛奖励">比赛奖励</a-option>
            <a-option value="红包抽奖">红包抽奖</a-option>
            <a-option value="匹配赛奖励">匹配赛奖励</a-option>
            <a-option value="其他">其他</a-option>
          </a-select>
        </a-form-item>
        <a-form-item field="reason" label="理由">
          <a-textarea
            v-model="kbReqForm.reason"
            placeholder="理由"
            :max-length="200"
            show-word-limit
          />
        </a-form-item>
        <multiple-img-uploader
          label="图片"
          prefix="kb"
          suffix="-cover.jpg"
          :required="false"
          v-model="kbReqForm.images"
        />
        <a-form-item field="comment" label="备注">
          <a-textarea
            v-model="kbReqForm.comment"
            placeholder="备注说明(客户不可见)"
            :max-length="200"
            show-word-limit
          />
        </a-form-item>
        <a-form-item>
          <a-button type="primary" html-type="submit" long :loading="submitting">
            K币 提交
            <template #icon>
              <IconPlus />
            </template>
          </a-button>
        </a-form-item>
      </a-form>
    </div>
  </a-drawer>
  <!-- kb 消费 drawer -->
  <a-drawer
    unmount-on-close
    v-model:visible="kbConsumeDrawerVisible"
    :title="`- K币 消费（${u.name || u.markName || u.mobile}）`"
    :width="500"
    :placement="isMdScreen ? 'right' : 'bottom'"
    height="90%"
    :footer="false"
  >
    <div v-if="u.userKbs">
      <div v-if="u.userKbs.length > 0">
        <div class="mb-4">
          <a-divider orientation="left">当前 K币</a-divider>
          <div class="space-y-2">
            <a-tag v-for="uk in u.userKbs" :key="uk.id"> {{ uk.hall.name }} : {{ uk.kb }} </a-tag>
          </div>
        </div>
      </div>
      <div v-else></div>
    </div>
    <a-divider orientation="left">消费 K币</a-divider>
    <a-form :model="kbConsumeForm" layout="horizontal" @submit="handleKbConsumeSubmit">
      <a-form-item
        field="hallId"
        label="选择门店"
        :rules="[{ required: true, message: '请选择门店' }]"
      >
        <a-select v-model="kbConsumeForm.hallId" placeholder="请选择门店">
          <a-option v-for="hall in account.halls ?? halls" :key="hall.id" :value="hall.id">
            {{ hall.name }}
          </a-option>
        </a-select>
      </a-form-item>
      <a-form-item
        field="kb"
        label="K币"
        :rules="[
          { required: true, message: '请输入K币数量' },
          { type: 'number', min: 1, message: 'K币数量必须大于0' },
        ]"
      >
        <a-input-number
          v-model="kbConsumeForm.kb"
          placeholder="请输入K币数量"
          :min="1"
          style="width: 100%"
        />
      </a-form-item>
      <a-form-item
        field="source"
        label="消费渠道"
        :rules="[{ required: true, message: '请选择消费渠道' }]"
      >
        <a-select v-model="kbConsumeForm.source" placeholder="请选择">
          <a-option value="兑换商品">兑换商品</a-option>
          <a-option value="兑换礼品">兑换礼品</a-option>
          <a-option value="抽奖">抽奖</a-option>
        </a-select>
      </a-form-item>
      <a-form-item field="reason" label="消费说明">
        <a-textarea
          v-model="kbConsumeForm.reason"
          placeholder="消费说明"
          :max-length="200"
          show-word-limit
        />
      </a-form-item>
      <a-form-item>
        <a-button type="primary" html-type="submit" long :loading="submitting">
          消费 K 币
          <template #icon>
            <IconMinus />
          </template>
        </a-button>
      </a-form-item>
    </a-form>
  </a-drawer>
  <!-- 消费积分 -->
  <a-drawer
    unmount-on-close
    v-model:visible="pointsDrawerVisible"
    :title="`消费积分（${u.name || u.markName || u.mobile} )`"
    :width="500"
    :placement="isMdScreen ? 'right' : 'bottom'"
    height="90%"
    :footer="false"
  >
    <div>
      <div class="mb-2">
        <span class="text-[var(--color-text-3)] font-semibold">当前积分：</span
        ><span class="font-bold text-[rgb(var(--primary-6))]">{{ u.currentPoints }}</span>
      </div>
      <a-form :model="pointsForm" layout="vertical" @submit="handlePointsSubmit">
        <a-form-item
          field="points"
          label="积分"
          :rules="[
            { required: true, message: '请输入积分' },
            { type: 'number', min: 1, message: '积分数量必须大于0' },
          ]"
        >
          <a-input-number
            v-model="pointsForm.points"
            placeholder="请输入积分数量"
            :min="1"
            :max="u.currentPoints ?? null"
          />
        </a-form-item>
        <a-form-item
          field="source"
          label="消费渠道"
          :rules="[{ required: true, message: '请选择消费渠道' }]"
        >
          <a-select v-model="pointsForm.source" placeholder="请选择">
            <a-option value="兑换礼品">兑换礼品</a-option>
          </a-select>
        </a-form-item>
        <a-form-item field="reason" label="消费说明">
          <a-textarea
            v-model="pointsForm.reason"
            placeholder="消费说明"
            :max-length="200"
            show-word-limit
          />
        </a-form-item>
        <a-form-item>
          <a-button type="primary" html-type="submit" long :loading="submitting">
            消费积分
            <template #icon>
              <IconMinus></IconMinus>
            </template>
          </a-button>
        </a-form-item>
      </a-form>
    </div>
  </a-drawer>
</template>
<script setup>
import { computed, ref, reactive, toRaw, watch } from 'vue'
import gql from 'graphql-tag'
import { usePageRequest } from '@/uses/page-request'
import { useRequest } from '@/uses/request'
import PaginationBar from './PaginationBar.vue'
import DateTime from './DateTime.vue'
import GenderLabel from '@/components/GenderLabel.vue'
import isMdScreen from '@/uses/is-md-screen'
import graphqlClient from '@/utils/graphql-client'
import { dayjs } from '@arco-design/web-vue/es/_utils/date'
import { userDetailFrag } from '@/gql/user'
import { Message } from '@arco-design/web-vue'
import client from '@/utils/graphql-client'
import { useHalls } from '@/uses/halls'
import { useAccount } from '@/uses/account'
const { halls, fetchHalls } = useHalls()
const { account } = useAccount()
import multipleImgUploader from './uploader/multiple-img-uploader.vue'
import { fetchCount } from '@/uses/kb-req-waiting-count'

const props = defineProps({
  user: Object,
  showPoints: { type: Boolean, default: false },
  showAddition: { type: Boolean, default: false },
})
watch(
  () => props.user,
  function () {
    userDetail.value = null
    kbLogsList.value = []
    kbLogsLoaded.value = false
    pointLogsList.value = []
    pointLogsLoaded.value = false
    checkinList.value = []
  },
)
const u = computed(() => (userDetail.value ? userDetail.value : props.user))
const descData = computed(() => [
  { label: '性别', value: u.value.gender === 1 ? '男' : u.value.gender === 2 ? '女' : '--' },
  { label: '生日', value: u.value.birthdayLabel || '--' },
  { label: '档位', value: u.value.level },
  { label: '爱好', value: u.value.hobbiesLabel?.length ? u.value.hobbiesLabel.join(' | ') : '--' },
])
const drawerVisible = ref(false)
const userDetail = ref(null)
const loading = ref(false)
const error = ref(null)
const doc = gql`
  query User($id: ID!) {
    user(id: $id) {
      ...UserDetailFields
    }
  }
  ${userDetailFrag}
`
function fetchUserDetail() {
  loading.value = true
  graphqlClient(doc, { id: props.user.id })
    .then((res) => {
      userDetail.value = res.user
      loading.value = false
    })
    .catch((err) => {
      loading.value = false
      error.value = err
    })
}
const showDrawer = () => {
  drawerVisible.value = true
  fetchUserDetail()
}
const tab = ref('tab')
function changeTab(e) {
  if (e === 'points' && !pointLogsLoaded.value) {
    pointLogsForm.userId = props.user.id
    searchPointLogs()
  } else if (e === 'kb' && !kbLogsLoaded.value) {
    kbLogsForm.userId = props.user.id
    searchKbLogs()
  } else if (e === 'checkin') {
    const d = dayjs()
    checkinVariables.start = d.format('YYYY-MM-01')
    checkinVariables.end = d.endOf('M').format('YYYY-MM-DD')
    checkinVariables.userId = props.user.id
  }
}
// kb logs
const docKbLogs = gql`
  query UserKbLogs($userId: ID, $hallId: ID, $page: Int, $size: Int) {
    userKbLogs(page: $page, size: $size, userId: $userId, hallId: $hallId) {
      total
      list {
        id
        kb
        source
        reason
        hall {
          id
          name
        }
        createdAt
        operator {
          id
          name
        }
        userKbReq {
          id
          createdAt
          createdBy {
            id
            name
          }
          approvedAt
          approvedBy {
            id
            name
          }
        }
      }
    }
  }
`
const {
  search: searchKbLogs,
  form: kbLogsForm,
  list: kbLogsList,
  total: kbLogsTotal,
  variables: kbLogsVariables,
  fetching: kbLogsFetching,
  loaded: kbLogsLoaded,
  goPage: kbLogsGoPage,
  error: kbLogsError,
} = usePageRequest(docKbLogs, { lazy: true })
// point logs
const docPointLogs = gql`
  query UserPointLogs(
    $userId: ID
    $page: Int
    $size: Int
    $type: String
    $name: String
    $mobile: String
  ) {
    userPointLogs(
      page: $page
      size: $size
      userId: $userId
      type: $type
      name: $name
      mobile: $mobile
    ) {
      total
      list {
        id
        points
        source
        reason
        operator {
          id
          name
        }
        createdAt
      }
    }
  }
`
const {
  search: searchPointLogs,
  form: pointLogsForm,
  list: pointLogsList,
  total: pointLogsTotal,
  variables: pointLogsVariables,
  loaded: pointLogsLoaded,
  goPage: pointLogsGoPage,
  fetching: pointLogsFetching,
  error: pointLogsError,
} = usePageRequest(docPointLogs, { lazy: true })
const docCheckins = gql`
  query CheckinList($start: String, $end: String, $userId: ID) {
    checkinList(size: 200, page: 1, userId: $userId, start: $start, end: $end) {
      total
      list {
        id
        hall {
          id
          name
          badgeColor
        }
        checkinDate
        checkinAt
      }
    }
  }
`
const {
  // error: checkinError,
  // loaded: checkinLoaded,
  // fetching: checkinFetching,
  data: { checkinList },
  variables: checkinVariables,
} = useRequest(docCheckins, { lazy: true })
const attributes = computed(() => {
  if (!halls.value || !checkinList.value) return []
  return halls.value.map((h) => ({
    dot: {
      style: {
        backgroundColor: h.badgeColor,
      },
    },
    dates: checkinList.value.list.filter((c) => c.hall.id === h.id).map((c) => c.checkinDate),
  }))
})
function moveCalendar(e) {
  const m = e[0].id
  const start = m + '-01'
  const end = dayjs(start).endOf('M').format('YYYY-MM-DD')
  checkinVariables.start = start
  checkinVariables.end = end
}
// 编辑用户信息开始
const editDrawerVisible = ref(false)
const userForm = reactive({
  markName: '',
  gender: 0,
  level: null,
})
function openEditDrawer() {
  userForm.markName = u.value.markName || ''
  userForm.gender = u.value.gender || 0
  userForm.level = u.value.level || 0
  editDrawerVisible.value = true
}
const submitting = ref(false)
const userSubmit = async function ({ errors }) {
  submitting.value = true
  if (errors) {
    submitting.value = false
    return
  }
  try {
    const doc = gql`
      mutation Mutation($id: ID!, $markName: String, $level: Float, $gender: Int) {
        updateUser(id: $id, markName: $markName, level: $level, gender: $gender) {
          ...UserDetailFields
        }
      }
      ${userDetailFrag}
    `
    const res = await client(doc, { ...userForm, id: props.user.id })
    Message.success('修改成功')
    const u = res.updateUser
    userDetail.value = u
    editDrawerVisible.value = false
  } catch (err) {
    // console.log(err)
    Message.error(err.message)
  }
  submitting.value = false
}
// 分配球柜
const lockerDrawerVisible = ref(false)

async function openLockerDrawer() {
  lockerDrawerVisible.value = true
  selectedLocker.value = null
  fetchHalls()
  fetchUserDetail()
}
const selectedHall = ref(0)
const selectedLocker = ref(null)
const handleAssignLocker = async () => {
  if (!selectedLocker.value) {
    Message.warning('请选择球柜')
    return
  }
  try {
    const res = await client(
      gql`
        mutation AssignLocker($userId: ID!, $lockerId: ID!) {
          assignLocker(userId: $userId, lockerId: $lockerId) {
            ...UserDetailFields
          }
        }
        ${userDetailFrag}
      `,
      {
        userId: props.user.id,
        lockerId: selectedLocker.value,
      },
    )
    userDetail.value = res.assignLocker
    Message.success('分配成功')
  } catch (err) {
    Message.error(err.message || '分配失败')
  }
}

const handleUnassignLocker = async (userLocker) => {
  try {
    const res = await client(
      gql`
        mutation UnassignLocker($id: ID!) {
          unassignLocker(id: $id) {
            ...UserDetailFields
          }
        }
        ${userDetailFrag}
      `,
      {
        id: userLocker.id,
      },
    )
    userDetail.value = res.unassignLocker
    // list.value = list.value.map((i) => (i.id === u.id ? u : i))
    Message.success('解除分配成功')
  } catch (err) {
    Message.error(err.message || '解除分配失败')
  }
}
// kb 提交
const kbReqDrawerVisible = ref(false)
const kbReqForm = reactive({
  hallId: '',
  kb: null,
  source: '',
  reason: '',
  comment: '',
  images: [],
})
const openKbReqDrawer = () => {
  kbReqDrawerVisible.value = true
  if (account.value.halls?.length === 1) {
    kbReqForm.hallId = account.value.halls[0].id
  }
}
const handleKbReqSubmit = async ({ errors }) => {
  if (errors) return
  submitting.value = true
  try {
    // addUserKbReq(hallId: ID!, kb: Int!, source: String!, reason: String = "", images: [String!], comment: String = ""): Boolean @admin
    await client(
      gql`
        mutation addUserKbReq(
          $hallId: ID!
          $userId: ID!
          $kb: Int!
          $source: String!
          $reason: String
          $comment: String
          $images: [String!]
        ) {
          addUserKbReq(
            hallId: $hallId
            userId: $userId
            kb: $kb
            source: $source
            reason: $reason
            comment: $comment
            images: $images
          ) {
            id
          }
        }
      `,
      {
        userId: props.user.id,
        ...toRaw(kbReqForm),
      },
    )
    Message.success('提交成功')
    kbReqDrawerVisible.value = false
    kbReqForm.kb = null
    kbReqForm.source = ''
    kbReqForm.reason = ''
    kbReqForm.comment = ''
    kbReqForm.images = []
    fetchCount()
  } catch (err) {
    Message.error(err.message || '提交失败')
  }
  submitting.value = false
}
// kb 消费
const kbConsumeDrawerVisible = ref(false)
const kbConsumeForm = reactive({
  hallId: '',
  kb: null,
  source: '',
  reason: '',
})
const openKbConsumeDrawer = () => {
  kbConsumeDrawerVisible.value = true
  fetchUserDetail()
  if (account.value.halls?.length === 1) {
    kbConsumeForm.hallId = account.value.halls[0].id
  }
}
const handleKbConsumeSubmit = async ({ errors }) => {
  if (errors) return
  submitting.value = true
  try {
    const res = await client(
      gql`
        mutation consumeUserKb(
          $hallId: ID!
          $userId: ID!
          $kb: Int!
          $source: String!
          $reason: String
        ) {
          consumeUserKb(
            hallId: $hallId
            userId: $userId
            kb: $kb
            source: $source
            reason: $reason
          ) {
            ...UserDetailFields
          }
        }
        ${userDetailFrag}
      `,
      {
        userId: props.user.id,
        ...toRaw(kbConsumeForm),
      },
    )
    Message.success('消费成功')
    kbConsumeDrawerVisible.value = false
    userDetail.value = res.consumeUserKb
    kbConsumeForm.kb = null
    kbConsumeForm.source = ''
    kbConsumeForm.reason = ''
    searchKbLogs()
    searchPointLogs()
  } catch (err) {
    Message.error(err.message || '消费失败')
  }
  submitting.value = false
}

// 积分消费
const pointsDrawerVisible = ref(false)
const pointsForm = reactive({
  points: null,
  source: '',
  reason: '',
})
const openPointsDrawer = () => {
  pointsDrawerVisible.value = true
  if (!userDetail.value) fetchUserDetail()
}
const handlePointsSubmit = async ({ errors }) => {
  if (errors) return
  submitting.value = true
  const doc = gql`
    mutation ConsumeUserPoints($userId: ID!, $points: Int!, $source: String!, $reason: String!) {
      consumeUserPoints(userId: $userId, source: $source, points: $points, reason: $reason) {
        ...UserDetailFields
      }
    }
    ${userDetailFrag}
  `
  try {
    const res = await client(doc, {
      userId: u.value.id,
      ...toRaw(pointsForm),
    })
    userDetail.value = res.consumeUserPoints
    pointsDrawerVisible.value = false
    Message.success('消费积分成功')
    pointsForm.points = null
    pointsForm.source = ''
    pointsForm.reason = ''
    searchPointLogs()
  } catch (err) {
    Message.error(err.message || '消费积分失败')
  }
  submitting.value = false
}
</script>
<style>
.hd-cell .arco-table-th-title {
  flex: 1;
}
</style>
