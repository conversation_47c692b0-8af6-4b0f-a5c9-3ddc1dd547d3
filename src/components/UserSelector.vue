<template>
  <div
    v-if="showUser && selectedUser"
    class="flex flex-1 items-center bg-[var(--color-bg-2)] px-2 py-2 rounded-lg border border-[var(--color-border-1)]"
  >
    <div class="flex-1">
      <user-avatar :user="selectedUser"></user-avatar>
    </div>
    <a-button type="primary" size="mini" @click="modalVisible = true">重选</a-button>
  </div>
  <div v-else>
    <a-button type="primary" size="mini" @click="modalVisible = true">{{ label }}</a-button>
  </div>
  <a-drawer
    title="选择用户"
    unmount-on-close
    v-model:visible="modalVisible"
    :width="500"
    :placement="isMdScreen ? 'right' : 'bottom'"
    height="90%"
    :footer="false"
  >
    <div class="grid grid-cols-2 gap-x-2 pb-4">
      <a-input v-model="form.mobile" placeholder="电话" allow-clear></a-input>
      <a-input v-model="form.name" placeholder="姓名" allow-clear></a-input>
    </div>
    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
      <div
        v-for="user in list"
        :key="user.id"
        class="flex flex-col justify-center bg-[var(--color-bg-2)] px-2 py-2 rounded-lg border border-[var(--color-border-1)]"
      >
        <div class="flex items-center">
          <div class="flex-1">
            <user-avatar :user="user" :show-points="false"></user-avatar>
          </div>

          <div>
            <a-button type="primary" size="mini" @click="handleSelect(user)">选择</a-button>
          </div>
        </div>
        <!--  -->
      </div>
    </div>
    <PaginationBar :total="total" :go-page="goPage" :page="variables.page" :size="variables.size" />
  </a-drawer>
</template>
<script setup>
import { ref, watch, toRaw } from 'vue'
import gql from 'graphql-tag'
import { usePageRequest } from '@/uses/page-request'
import { userShortFrag } from '@/gql/user'
import UserAvatar from './UserAvatar.vue'
import isMdScreen from '@/uses/is-md-screen'
const userId = defineModel({ type: String, default: '' })
const props = defineProps({
  defaultUser: Object,
  label: { type: String, default: '选择' },
  showUser: { type: Boolean, default: true },
})
const emits = defineEmits(['selected'])
console.log(props)
const selectedUser = ref(props.defaultUser ? { ...props.defaultUser } : null)
const modalVisible = ref(false)
const doc = gql`
  query Users($page: Int, $name: String, $mobile: String) {
    users(page: $page, size: 10, name: $name, mobile: $mobile) {
      total
      list {
        ...UserFields
      }
    }
  }
  ${userShortFrag}
`
const { list, total, search, form, variables, goPage } = usePageRequest(doc)
watch(form, () => {
  search()
})
function handleSelect(user) {
  selectedUser.value = user
  userId.value = user.id
  modalVisible.value = false
  emits('selected', toRaw(user))
}
</script>
