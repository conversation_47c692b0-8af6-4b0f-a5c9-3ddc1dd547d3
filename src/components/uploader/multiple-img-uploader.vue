<template>
  <a-form-item :field="field" :label="label" :rules="rules">
    <a-upload
      v-model:file-list="files"
      action="https://upload.qiniup.com/"
      :on-before-upload="beforeUpload"
      :data="uploadData"
      :limit="limit"
      :multiple="true"
      list-type="picture-card"
      image-preview
    >
      <template #image="{ fileItem }">
        <img v-if="fileItem.url" :src="fileItem.file ? fileItem.url : fileItem.url + suffix" />
      </template>
    </a-upload>
  </a-form-item>
</template>
<script setup>
import { ref, reactive, watch } from 'vue'
import gql from 'graphql-tag'
import request from '@/utils/graphql-client'

const props = defineProps({
  field: String,
  label: String,
  limit: { type: Number, default: 12 },
  prefix: { type: String, default: '' },
  suffix: { type: String },
  required: { type: Boolean, default: true },
})
const rules = [
  {
    required: props.required,
    message: '请上传图片',
  },
  {
    validator: (value, cb) => {
      // 'init' | 'uploading' | 'done' | 'error';
      const errors = files.value.filter((i) => i.status && i.status !== 'done')
      if (errors.length > 0) {
        const s = errors[0].status
        cb(s === 'error' ? '图片上传失败' : '图片上传中...')
      } else {
        cb()
      }
    },
  },
]
const imgs = defineModel({ type: Array, default: [] })
const files = ref(imgs.value ? imgs.value.map((url, index) => ({ url, uid: `u${index}` })) : [])
// 上传相关
const uploadData = reactive({ token: '' })
function beforeUpload() {
  if (uploadData.token) return Promise.resolve(true)
  return request(
    gql`
      query Query($prefix: String!) {
        qiniuToken(prefix: $prefix)
      }
    `,
    { prefix: props.prefix },
  ).then((res) => {
    uploadData.token = res.qiniuToken
    return Promise.resolve(true)
  })
}
watch(
  files,
  (v) => {
    if (!v.length) {
      imgs.value = []
    } else {
      imgs.value = v.map((i) => (i.response ? `${i.response.host}${i.response.key}` : i.url))
    }
  },
  { immediate: false },
)
</script>
<style scoped>
:deep(.arco-upload-list-picture) img {
  object-fit: contain;
}

:deep(.arco-upload-list-picture) {
  background-color: var(--color-fill-2);
}
</style>
