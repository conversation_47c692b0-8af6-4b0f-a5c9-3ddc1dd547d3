<template>
  <a-form-item :field="field" :label="label" :rules="rules">
    <a-upload
      v-model:file-list="files"
      action="https://upload.qiniup.com/"
      :on-before-upload="beforeUpload"
      :data="uploadData"
      :limit="1"
      :multiple="false"
      list-type="picture-card"
      image-preview
    >
      <template #image="{ fileItem }">
        <img v-if="fileItem.url" :src="fileItem.file ? fileItem.url : fileItem.url + suffix" />
      </template>
    </a-upload>
  </a-form-item>
</template>
<script setup>
import { ref, reactive, watch } from 'vue'
import gql from 'graphql-tag'
import request from '@/utils/graphql-client'

const props = defineProps({
  field: String,
  label: String,
  prefix: { type: String, default: '' },
  suffix: { type: String },
})
const rules = [
  {
    required: true,
    message: '请上传图片',
  },
  {
    validator: (value, cb) => {
      // 'init' | 'uploading' | 'done' | 'error';
      const errors = files.value.filter((i) => i.status && i.status !== 'done')
      if (errors.length > 0) {
        const s = errors[0].status
        cb(s === 'error' ? '图片上传失败' : '图片上传中...')
      } else {
        cb()
      }
    },
  },
]
const img = defineModel({ type: String, default: '' })
const files = ref(img.value ? [{ uid: '1', url: img.value }] : [])
// 上传相关
const uploadData = reactive({ token: '' })
function beforeUpload() {
  if (uploadData.token) return Promise.resolve(true)
  return request(
    gql`
      query Query($prefix: String! = "courses") {
        qiniuToken(prefix: $prefix)
      }
    `,
    { prefix: props.prefix },
  ).then((res) => {
    uploadData.token = res.qiniuToken
    return Promise.resolve(true)
  })
}
watch(
  files,
  (v) => {
    if (!v.length) {
      img.value = ''
    } else {
      img.value = v[0].response?.key
        ? `${v[0].response.imageHost}${v[0].response.key}`
        : v[0].url || ''
    }
  },
  { immediate: false },
)
</script>
<style scoped>
:deep(.arco-upload-list-picture) img {
  object-fit: contain;
}

:deep(.arco-upload-list-picture) {
  background-color: var(--color-fill-2);
}
</style>
