// import { createDirective } from '../utils/private/create.js'
import { markRaw } from 'vue'

import debounce from './utils/debounce.js'
import { height, offset } from './utils/dom.js'
import { getScrollTarget } from './utils/scroll.js'
import { listenOpts } from './utils/event.js'

// export const createComponent = raw => markRaw(defineComponent(raw))
export const createDirective = (raw) => markRaw(raw)
// import getSSRProps from '../utils/private/noop-ssr-directive-transform.js'

const { passive } = listenOpts

function update(ctx, { value, oldValue }) {
  if (typeof value !== 'function') {
    ctx.scrollTarget.removeEventListener('scroll', ctx.scroll, passive)
    return
  }

  ctx.handler = value
  if (typeof oldValue !== 'function') {
    ctx.scrollTarget.addEventListener('scroll', ctx.scroll, passive)
    ctx.scroll()
  }
}

export default {
  // name: 'scroll-fire',

  mounted(el, binding) {
    const ctx = {
      scrollTarget: getScrollTarget(el),
      multiple: binding.arg === 'multiple',
      scroll: debounce(() => {
        let containerBottom, elBottom

        if (ctx.scrollTarget === window) {
          elBottom = el.getBoundingClientRect().bottom
          containerBottom = window.innerHeight
        } else {
          elBottom = offset(el).top + height(el)
          containerBottom = offset(ctx.scrollTarget).top + height(ctx.scrollTarget)
        }

        if (elBottom > 0 && elBottom < containerBottom) {
          if (!ctx.multiple) {
            ctx.scrollTarget.removeEventListener('scroll', ctx.scroll, passive)
          }
          // console.log('scroll-fire -- multiple', ctx.multiple)
          ctx.handler(el)
        }
      }, 25),
    }

    update(ctx, binding)

    el.__qscrollfire = ctx
  },

  updated(el, binding) {
    if (binding.value !== binding.oldValue) {
      update(el.__qscrollfire, binding)
    }
  },

  beforeUnmount(el) {
    const ctx = el.__qscrollfire
    if (!ctx) return
    ctx.scrollTarget.removeEventListener('scroll', ctx.scroll, passive)
    ctx.scroll.cancel()
    delete el.__qscrollfire
  },
}
