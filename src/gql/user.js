import gql from 'graphql-tag'

export const userShortFrag = gql`
  fragment UserFields on User {
    id
    mobile
    name
    markName
    avatar
    currentPoints
    accumulatedPoints
    registered
    profileCompleted
  }
`

export const userThumbFrag = gql`
  fragment UserThumbFields on User {
    id
    mobile
    name
    markName
    gender
    level
    birthday
    birthdayLabel
    avatar
    registered
    profileCompleted
    currentPoints
    accumulatedPoints
  }
`

export const userDetailFrag = gql`
  fragment UserDetailFields on User {
    id
    openId
    unionId
    mobile
    mobileCountry
    name
    markName
    avatar
    gender
    birthday
    birthdayLabel
    createdAt
    createdBy {
      id
      name
    }
    registered
    registeredAt
    profileCompleted
    level
    currentPoints
    accumulatedPoints
    accumulatedPointsRank
    userLockers {
      id
      locker {
        lockerNo
        id
        remark
        hall {
          id
          name
        }
      }
    }
    hobbies
    hobbiesLabel
    userKbs {
      id
      kb
      rank
      hall {
        id
        name
      }
    }
  }
`
