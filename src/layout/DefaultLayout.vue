<template>
  <div v-if="!loaded">
    <a-spin />
  </div>
  <div v-else-if="error">
    <a-result status="500" title="登录错误" :sub-title="error.message" />
  </div>
  <div v-else class="flex flex-col">
    <div class="bg-[var(--color-menu-light-bg)] fixed inset-x-0 top-0 z-10">
      <div class="flex max-w-6xl mx-auto">
        <div class="flex-1">
          <a-menu mode="horizontal" :selected-keys="[menuKey]">
            <router-link to="/ranking">
              <a-menu-item key="/ranking">排行榜</a-menu-item>
            </router-link>
            <router-link to="/users">
              <a-menu-item key="/users">客户</a-menu-item>
            </router-link>
            <router-link to="/kbs-req" v-permission="['operator']">
              <a-badge :count="kbCount">
                <a-menu-item key="/kbs-req">K币提交</a-menu-item>
              </a-badge>
            </router-link>
            <router-link to="/kbs-req" v-permission="['admin', 'executor']">
              <a-badge :count="kbCount">
                <a-menu-item key="/kbs-req">K币审核</a-menu-item>
              </a-badge>
            </router-link>
            <router-link to="/kbs">
              <a-menu-item key="/kbs">K币</a-menu-item>
            </router-link>
            <router-link to="/points">
              <a-menu-item key="/points">积分</a-menu-item>
            </router-link>
            <router-link to="/checkin">
              <a-menu-item key="/checkin">签到</a-menu-item>
            </router-link>
            <router-link to="/suggestion">
              <a-menu-item key="/suggestion">建议</a-menu-item>
            </router-link>
            <router-link to="/tournaments">
              <a-badge :count="tournamentCount">
                <a-menu-item key="/tournaments">赛事</a-menu-item>
              </a-badge>
            </router-link>
            <router-link to="/settings/lockers">
              <a-menu-item key="/settings/lockers">球杆柜</a-menu-item>
            </router-link>
            <router-link to="/settings/admins" v-permission="['admin', 'executor']">
              <a-menu-item key="/settings/admins">管理员</a-menu-item>
            </router-link>
          </a-menu>
        </div>
        <div class="pr-6 hidden md:block">
          <a-dropdown>
            <div
              class="cursor-pointer py-[14px] box-content pr-sm"
              style="line-height: 30px; height: 30px"
            >
              <span class="mr-1">{{ account.name }}</span
              ><icon-down />
            </div>

            <template #content>
              <a-doption @click="pwdModalVisible = true">
                <template #icon><icon-settings /></template>修改密码
              </a-doption>
              <a-divider :margin="2" />
              <a-doption @click="clickLogout">
                <template #icon><icon-export /></template> 退出登录
              </a-doption>
            </template>
          </a-dropdown>
        </div>
      </div>
    </div>
    <div class="flex-1 w-full max-w-6xl mx-auto px-2 pb-4 pt-2 md:pt-4" style="margin-top: 58px">
      <div class="flex md:hidden">
        <div class="flex-1">
          <a-breadcrumb>
            <a-breadcrumb-item>首页</a-breadcrumb-item>
            <a-breadcrumb-item>{{ route.meta.title }}</a-breadcrumb-item>
          </a-breadcrumb>
        </div>
        <div class="pr-2">
          <a-dropdown>
            <div class="cursor-pointer box-content pr-sm" style="line-height: 30px; height: 30px">
              <span class="mr-1">{{ account.name }}</span
              ><icon-down />
            </div>

            <template #content>
              <a-doption @click="pwdModalVisible = true">
                <template #icon><icon-settings /></template>修改密码
              </a-doption>
              <a-divider :margin="2" />
              <a-doption @click="clickLogout">
                <template #icon><icon-export /></template> 退出登录
              </a-doption>
            </template>
          </a-dropdown>
        </div>
      </div>
      <RouterView />
    </div>
    <a-back-top />
  </div>
  <a-modal
    v-model:visible="pwdModalVisible"
    :footer="false"
    title="修改密码"
    width="500px"
    :modal-style="{ maxWidth: '100vw' }"
  >
    <a-form
      :model="form"
      :label-col-props="{ span: 6 }"
      :wrapper-col-props="{ span: 16 }"
      @submit="savePassword"
    >
      <a-form-item
        field="oldPassword"
        label="旧密码"
        :rules="[{ required: true, message: '请输入旧密码' }]"
      >
        <a-input-password
          v-model="form.oldPassword"
          placeholder="请输入旧密码"
          autocomplete="current-password"
        />
      </a-form-item>
      <a-form-item field="password" label="新密码" :rules="rule1">
        <a-input-password
          v-model="form.password"
          placeholder="请输入新密码"
          autocomplete="new-password"
        />
      </a-form-item>
      <a-form-item>
        <a-button html-type="submit" type="primary" long>修改密码</a-button>
      </a-form-item>
      <a-form-item field="password" label="">
        <div class="text-arco-text-2">密码要求：长度至少 6 位</div>
      </a-form-item>
    </a-form>
  </a-modal>
</template>
<script setup>
import { ref, computed, reactive, onMounted, onUnmounted, onBeforeUnmount } from 'vue'
import { useRoute } from 'vue-router'
import { useAccount } from '@/uses/account.js'
import { Message } from '@arco-design/web-vue'
const { account, loaded, error, logout, changePassword } = useAccount()
import { start, stop, kbCount, tournamentCount } from '@/uses/kb-req-waiting-count.js'
// const { } = useKbReqWaitingCount()
const route = useRoute()
const menuKey = computed(() => {
  if (!route.path) return '/'
  return route.path
})
function clickLogout() {
  logout().then(() => {
    Message.success('已退出登录')
    setTimeout(() => {
      window.location.reload()
    }, 1500)
  })
}
const pwdModalVisible = ref(false)
const rule1 = [
  { required: true, message: '请输入新密码' },
  { minLength: 6, message: '长度字少 6 位' },
]
const form = reactive({ oldPassword: '', password: '' })
function savePassword({ values, errors }) {
  if (errors) return
  changePassword(values.oldPassword, values.password)
    .then(() => {
      Message.success('密码修改成功')
      pwdModalVisible.value = false
    })
    .catch((err) => {
      Message.error(err.message)
    })
}
onMounted(() => {
  console.log('layout on mounted')
  start()
})
onUnmounted(() => {
  console.log('layout on unmounted')
  stop()
})
onBeforeUnmount(() => {
  console.log('layout on before unmounted')
  stop()
})
</script>
<style>
.arco-dropdown-open .arco-icon-down {
  transform: rotate(180deg);
}
</style>
