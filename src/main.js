import './assets/main.css'

import { createApp } from 'vue'
import App from './App.vue'
import PageLoading from './components/PageLoading.vue'
import DateTime from './components/DateTime.vue'
import PaginationBar from './components/PaginationBar.vue'
import VCalendar from 'v-calendar'
import 'v-calendar/style.css'

import router from './router'
import directive from './directive'
const app = createApp(App)

app.use(router)
app.use(directive)
app.use(VCalendar, {})
app.component('PageLoading', PageLoading)
app.component('DateTime', DateTime)
app.component('PaginationBar', PaginationBar)
app.mount('#app')
