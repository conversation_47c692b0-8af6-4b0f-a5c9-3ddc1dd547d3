import { createRouter, createWebHistory } from 'vue-router'
import { useAccount } from '@/uses/account.js'
import DefaultLayout from '@/layout/DefaultLayout.vue'
const router = createRouter({
  history: createWebHistory(import.meta.env.BASE_URL),
  routes: [
    {
      path: '/',
      name: 'manage',
      redirect: '/ranking',
      component: DefaultLayout,
      children: [
        {
          path: 'ranking',
          name: 'ranking',
          meta: {
            auth: true,
            title: '排行榜',
          },
          component: () => import('@/views/RankingView.vue'),
        },
        {
          path: 'users',
          name: 'users',
          meta: {
            auth: true,
            title: '客户',
          },
          component: () => import('@/views/UsersView.vue'),
        },
        {
          path: 'points',
          name: 'points',
          meta: {
            auth: true,
            title: '积分',
          },
          component: () => import('@/views/PointsView.vue'),
        },
        {
          path: 'checkin',
          name: 'checkin',
          meta: {
            auth: true,
            title: '签到',
          },
          component: () => import('@/views/CheckinView.vue'),
        },
        {
          path: 'suggestion',
          name: 'suggestion',
          meta: {
            auth: true,
            title: '建议',
          },
          component: () => import('@/views/SuggestionView.vue'),
        },
        {
          path: 'kbs-req',
          name: 'kbs-req',
          meta: {
            auth: true,
            title: 'K币提交审核',
          },
          component: () => import('@/views/KbReqList.vue'),
        },
        {
          path: 'kbs',
          name: 'kbs',
          meta: {
            auth: true,
            title: 'K币',
          },
          component: () => import('@/views/KbsView.vue'),
        },
        {
          path: 'tournaments',
          name: 'tournaments',
          meta: {
            auth: true,
            title: '赛事',
          },
          component: () => import('@/views/tournaments/TournamentsView.vue'),
        },
        {
          path: 'settings/admins',
          name: 'admins',
          meta: {
            auth: true,
            title: '管理员',
          },
          component: () => import('@/views/AdminsView.vue'),
        },
        {
          path: 'settings/lockers',
          name: 'lockers',
          meta: {
            auth: true,
            title: '球杆柜',
          },
          component: () => import('@/views/LockersView.vue'),
        },
        // {
        //   path: '/change-password',
        //   name: 'change-password',
        //   meta: { auth: true },
        //   component: () => import('@/views/manage/ChangePassword.vue'),
        // },
      ],
    },
    {
      path: '/account/login',
      name: 'login',
      meta: {
        auth: false,
      },
      component: () => import('@/views/account/LoginView.vue'),
    },
    {
      path: '/:pathMatch(.*)*',
      name: 'not-found',
      component: () => import('@/views/NotFoundView.vue'),
    },
  ],
})

router.beforeEach(async function (to) {
  if (!to.meta.auth) {
    return true
  }
  const { account, fetchAccount } = useAccount()
  if (account.value) {
    return true
  }
  try {
    await fetchAccount()
    return account.value ? true : '/account/login'
  } catch (e) {
    console.log(e)
    return '/account/login'
  }
})
export default router
