import { ref } from 'vue'
import gql from 'graphql-tag'
import client from '@/utils/graphql-client'

const account = ref(null)
const loaded = ref(false)
const error = ref(null)
const submitting = ref(false)
const fetchAccount = async () => {
  const doc = gql`
    query CurrentAccount {
      account {
        id
        mobile
        name
        role
        halls {
          id
          name
        }
      }
    }
  `
  try {
    const res = await client(doc)
    loaded.value = true
    error.value = null
    account.value = res.account
  } catch (err) {
    console.log(err)
    loaded.value = true
    error.value = err
  }
}

const login = async (mobile, password) => {
  console.log('login', mobile, password)
  const doc = gql`
    mutation Mutation($mobile: String!, $password: String!) {
      login(mobile: $mobile, password: $password) {
        id
        mobile
        name
        role
      }
    }
  `
  const res = await client(doc, { mobile, password })
  account.value = res.login
  loaded.value = true
}

const logout = async () => {
  const doc = gql`
    mutation Logout {
      logout
    }
  `
  await client(doc)
  // account.value = null
  loaded.value = true
  error.value = null
}

const changePassword = async (oldPassword, password) => {
  const doc = gql`
    mutation ChangePassword($oldPassword: String!, $password: String!) {
      changePassword(oldPassword: $oldPassword, password: $password)
    }
  `
  submitting.value = true
  await client(doc, { oldPassword, password })
  error.value = null
  submitting.value = false
}
export const useAccount = () => {
  return { account, loaded, error, fetchAccount, login, logout, changePassword }
}
