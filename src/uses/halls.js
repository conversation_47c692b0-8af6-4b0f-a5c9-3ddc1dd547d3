import { ref, shallowRef } from 'vue'
import gql from 'graphql-tag'
import client from '@/utils/graphql-client'
const halls = shallowRef([])
const fetching = ref(false)
const fetchHalls = async () => {
  try {
    fetching.value = true
    const res = await client(gql`
      query Halls {
        halls {
          id
          name
          badgeColor
          lockers {
            id
            lockerNo
            remark
            status
            userLocker {
              id
              user {
                id
              }
            }
          }
        }
      }
    `)
    halls.value = res.halls
    fetching.value = false
  } catch {
    fetching.value = false
  }
}
export const useHalls = function () {
  if (!fetching.value && !halls.value.length) {
    fetchHalls()
  }
  return { halls }
}
