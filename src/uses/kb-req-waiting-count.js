import { ref } from 'vue'
import gql from 'graphql-tag'
import client from '@/utils/graphql-client'
const kbCount = ref(null)
const tournamentCount = ref(null)
const fetchCount = async () => {
  const res = await client(gql`
    query Count {
      userKbReqWaitingApproveCount
      tournamentWaitingApproveCount
    }
  `)
  kbCount.value = res.userKbReqWaitingApproveCount
  tournamentCount.value = res.tournamentWaitingApproveCount
}
let timer = null
function start() {
  fetchCount()
  if (timer) clearInterval(timer)
  timer = setInterval(() => {
    start()
  }, 30 * 1000)
}
function stop() {
  if (timer) clearInterval(timer)
}
// export const useKbReqWaitingCount = function () {
//   return
// }

export { start, stop, fetchCount, kbCount, tournamentCount }
