import { ref, toRaw, reactive } from 'vue'
import request from '../utils/graphql-client'

/**
 * 用于处理分页请求的自定义 Hook
 * @param {Object} doc - GraphQL 查询文档
 * @param {Object} options - 配置选项（可选）
 * @returns {Object} 返回一个包含以下属性的对象：
 *   - loaded {Ref<boolean>} 是否已加载数据
 *   - fetching {Ref<boolean>} 是否正在获取数据
 *   - error {Ref<Error|null>} 错误信息
 *   - list {Ref<Array>} 当前页的数据列表
 *   - total {Ref<number>} 总记录数
 *   - pageCount {ComputedRef<number>} 总页数
 *   - variables {Reactive<Object>} 查询参数
 *   - form {Reactive<Object>} 表单查询对象
 *   - search {Function} 搜索函数，用于触发新的查询
 */

export const usePageRequest = function (doc, options = { lazy: false }) {
  const loaded = ref(false)
  const fetching = ref(false)
  const error = ref(null)
  const list = ref([])
  const total = ref(0)
  // 查询参数
  const variables = reactive({})
  // 表单查询对象
  const form = reactive({})
  // form 的初始值
  const formInit = {}

  const {
    selectionSet: { selections },
    variableDefinitions,
  } = doc.definitions[0]
  function getDv(item) {
    if (item.kind === 'IntValue') return parseInt(item.value)
    if (item.kind === 'ListValue') {
      return item.values.map((i) => getDv(i))
    }
    return item.value
  }
  for (let i = 0; i < variableDefinitions.length; i++) {
    const v = variableDefinitions[i]
    const name = v.variable.name.value
    const defaultValue = v.defaultValue === undefined ? null : getDv(v.defaultValue)
    variables[name] = defaultValue
    if (name !== 'size' && name !== 'page') {
      form[name] = defaultValue
      formInit[name] = defaultValue
    }
  }
  const defaultPageSize = variables.size || 20
  if (!variables.page) variables.page = 1
  if (!variables.size) variables.size = defaultPageSize
  const fieldName = selections[0].name.value
  function resetForm(excludes = []) {
    Object.keys(form).forEach((key) => {
      if (excludes.includes(key)) return
      form[key] = formInit[key]
    })
  }
  function search() {
    if (fetching.value) return
    fetching.value = true
    request(doc, { page: 1, size: variables.size, ...toRaw(form) })
      .then((res) => {
        list.value = res[fieldName].list
        total.value = res[fieldName].total
        if (!loaded.value) loaded.value = true
        fetching.value = false
        Object.keys(form).forEach((key) => {
          variables[key] = form[key]
        })
        variables.page = 1
        if (error.value) error.value = null
      })
      .catch((err) => {
        error.value = err
        // if (!loaded.value) loaded.value = true
        fetching.value = false
      })
  }
  function goPage(page, size, mergeList = false) {
    console.log('go page', page, size)
    if (fetching.value) return
    fetching.value = true
    const lastPage = variables.page
    const lastSize = variables.size
    variables.page = page
    variables.size = size
    request(doc, { ...toRaw(variables), page, size })
      .then((res) => {
        list.value = mergeList ? list.value.concat(res[fieldName].list) : res[fieldName].list
        total.value = res[fieldName].total
        fetching.value = false
        // Object.keys(form).forEach((key) => {
        //   variables[key] = form[key]
        // })
        if (error.value) error.value = null
      })
      .catch((err) => {
        variables.page = lastPage
        variables.size = lastSize
        error.value = err
        fetching.value = false
      })
  }
  if (!options.lazy) search()
  return { loaded, error, list, total, search, form, resetForm, variables, goPage, fetching }
}
