import { ref, reactive, shallowRef, isRef, watch, unref } from 'vue'
import client from '@/utils/graphql-client'

/**
 *
 * @param {import('graphql-tag').DocumentNode} doc GraphQL tag 对象
 * @param {object} variables GraphQL 查询变量
 * @param {object} options 配置选项
 * @param {boolean} options.lazy 是否延迟加载
 * @returns {{
 *   loaded: import('vue').Ref<boolean>,
 *   fetching: import('vue').Ref<boolean>,
 *   error: import('vue').Ref<Error | null>,
 *   data: Record<string, import('vue').Ref>,
 *   variables: Record<string, any>,
 *   fetch: () => Promise<void>
 * }} 包含请求状态、数据和操作方法的对象
 */
export function useRequest(doc, options = {}) {
  const loaded = ref(false)
  const fetching = ref(true)
  const error = ref(null)

  const {
    selectionSet: { selections },
    variableDefinitions,
  } = doc.definitions[0]
  // 生成变量
  const variables = reactive({})
  function getDv(item) {
    if (item.kind === 'IntValue') return parseInt(item.value)
    if (item.kind === 'ListValue') {
      return item.values.map((i) => getDv(i))
    }
    return item.value
  }
  for (let i = 0; i < variableDefinitions.length; i++) {
    const v = variableDefinitions[i]
    const name = v.variable.name.value
    const defaultValue = !v.defaultValue ? null : getDv(v.defaultValue)
    variables[name] = defaultValue
  }
  // 拼装返回数据
  const data = {}
  const keys = []
  for (let i = 0; i < selections.length; i++) {
    const { alias, name } = selections[i]
    keys.push(alias ? alias.value : name.value)
  }
  for (let i = 0; i < keys.length; i++) {
    data[keys[i]] = shallowRef()
  }
  function fetch() {
    fetching.value = true
    client(doc, unref(variables))
      .then((res) => {
        for (let i = 0; i < keys.length; i++) {
          const key = keys[i]
          if (isRef(data[key])) {
            data[key].value = res[key]
          } else {
            data[key] = res[key]
          }
        }
        loaded.value = true
        fetching.value = false
        error.value = null
      })
      .catch((err) => {
        error.value = err
        loaded.value = true
        fetching.value = false
      })
  }
  watch(variables, fetch)
  if (!options.lazy) fetch()
  return { loaded, fetching, error, data, fetch, variables }
}
