import { watch, reactive, ref, unref } from 'vue'
import request from '../utils/graphql-client'

function isObject(value) {
  return value !== null && typeof value === 'object' && !Array.isArray(value)
}
function diff(cur, pre) {
  if (Array.isArray(cur) && Array.isArray(pre)) {
    if (cur.length === pre.length) {
      for (let i = 0; i < cur.length; i++) {
        const d = diff(cur[i], pre[i])
        if (d) return d
      }
    } else {
      return true
    }
  } else if (isObject(cur) && isObject(pre)) {
    for (const key in cur) {
      const d = diff(cur[key], pre[key])
      if (d) return d
    }
  } else {
    return cur !== pre
  }
}

/**
 * 自动watch variables 变化，生产相应的doc，重新请求数据，无冗余
 * @param doc graphql-doc
 * @param {{initData: Object, watch: Boolean, lazy: <PERSON>olean}} [option] 初始值
 * @return {{loaded: Ref<UnwrapRef<boolean>>, variables: UnwrapNestedRefs<{}>, data: {}, fetching: Ref<UnwrapRef<boolean>>, reFetchAll: reFetchAll, error: Ref<UnwrapRef<null>>, fetchSelections: fetchSelections}}
 */
export default function (doc, option = {}) {
  option.initData = option.initData || {}
  option.watch = option.watch !== undefined ? !!option.watch : true
  option.lazy = !!option.lazy
  const {
    selectionSet: { selections },
    variableDefinitions,
  } = doc.definitions[0]
  const argKeys = {}
  const variables = reactive({})
  const loaded = ref(false)
  const fetching = ref(true)
  const error = ref(null)
  const data = {}
  const variableDefinitionsBack = []
  function getDv(item) {
    if (item.kind === 'IntValue') return parseInt(item.value)
    if (item.kind === 'ListValue') {
      return item.values.map((i) => getDv(i))
    }
    return item.value
  }
  for (let i = 0; i < variableDefinitions.length; i++) {
    const v = variableDefinitions[i]

    variableDefinitionsBack.push(v)
    const name = v.variable.name.value
    const defaultValue = !v.defaultValue ? null : getDv(v.defaultValue)
    argKeys[name] = { variable: i, selections: [] }
    variables[name] = defaultValue
  }
  const selectionsBack = []
  for (let i = 0; i < selections.length; i++) {
    const selection = selections[i]
    const key = selection.alias ? selection.alias.value : selection.name.value
    data[key] = option.initData[key] ? ref(option.initData[key]) : ref()
    selectionsBack.push(selection)
    for (const arg of selection.arguments) {
      const { kind, value } = arg
      if (kind === 'Argument' && value.kind === 'Variable') {
        argKeys[value.name.value].selections.push(i)
      }
    }
  }
  function doFetch(theDoc, vars) {
    fetching.value = true
    request(theDoc, vars || unref(variables))
      .then((res) => {
        for (const k in res) {
          data[k].value = res[k]
        }
        if (!loaded.value) loaded.value = true
        if (error.value) error.value = null
        fetching.value = false
        // if (!option.watch)
      })
      .catch((err) => {
        error.value = err
        loaded.value = true
        fetching.value = false
      })
  }
  if (!option.lazy) doFetch(doc)
  let lastVar = JSON.stringify(variables)
  if (option.watch) {
    watch(variables, (newValue) => {
      if (!loaded.value) {
        doFetch(doc)
        return
      }
      const oldValue = JSON.parse(lastVar)
      const diffKeys = []
      for (const k in newValue) {
        const d = diff(newValue[k], oldValue[k])
        if (d) diffKeys.push(k)
      }
      lastVar = JSON.stringify(newValue)
      if (diffKeys.length) {
        // const temp = diffKeys.reduce((m, i) => {
        //   if (!m.vars.includes(argKeys[i].variable)) m.vars.push(argKeys[i].variable)
        //   argKeys[i].selections.forEach(s => {
        //     if (!m.selections.includes(s)) m.selections.push(s)
        //   })
        //   return m
        // }, { vars: [], selections: [] })
        console.log(argKeys)
        const selectionIds = diffKeys.reduce((memo, item) => {
          argKeys[item].selections.forEach((s) => {
            if (!memo.includes(s)) memo.push(s)
          })
          return memo
        }, [])
        const selections = selectionIds.map((i) => selectionsBack[i])
        const varsNames = selections.reduce((memo, item) => {
          for (const arg of item.arguments) {
            const { kind, value } = arg
            if (kind === 'Argument' && value.kind === 'Variable') {
              if (!memo.includes(value.name.value)) memo.push(value.name.value)
            }
          }
          return memo
        }, [])
        doc.definitions[0].variableDefinitions = varsNames.map(
          (i) => variableDefinitionsBack[argKeys[i].variable],
        )
        doc.definitions[0].selectionSet.selections = selections
        doFetch(doc)
      }
    })
  }
  function fetchSelections(args, selectionNames) {
    if (!selectionNames || selectionNames.length === 0) {
      doFetch(doc, args)
      return
    }
    const vars = []
    const selections = selectionNames.map((name) => {
      const s = selectionsBack.find((i) => (i.alias ? i.alias === name : i.name.value === name))
      s.arguments.forEach((a) => {
        if (a.value.kind === 'Variable') {
          if (!vars.includes(argKeys[a.value.name.value].variable))
            vars.push(argKeys[a.value.name.value].variable)
        }
      })
      return s
    })
    doc.definitions[0].variableDefinitions = vars.map((i) => variableDefinitionsBack[i])
    doc.definitions[0].selectionSet.selections = selections
    doFetch(doc, args)
  }
  function reFetchAll() {
    doc.definitions[0].variableDefinitions = variableDefinitionsBack
    doc.definitions[0].selectionSet.selections = selectionsBack
    doFetch(doc)
  }
  return { error, loaded, fetching, data, variables, reFetchAll, fetchSelections }
}
