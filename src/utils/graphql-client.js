import { GraphQLClient } from 'graphql-request'

const domain = `${window.location.protocol}//${window.location.hostname}${window.location.port ? ':' + window.location.port : ''}`

export default async function () {
  const abortController = new AbortController()
  let client = new GraphQLClient(domain + '/graphql', {
    signal: abortController.signal,
  })
  const t = setTimeout(() => abortController.abort(), 20000)
  try {
    const res = await client.request(...arguments)
    return res
  } catch (error) {
    console.log(error)
    if (error.message === 'Aborted' || error.message.toLowerCase().indexOf('aborted') >= 0) {
      throw { message: '请求超时' }
    }
    if (error.response) {
      if (error.response.status === 400) {
        throw { message: '参数错误' }
      } else if (error.response.errors) {
        throw { message: error.response.errors[0].message }
      } else {
        throw { message: '服务器出错了' }
      }
    } else {
      throw { message: error.message }
    }
  } finally {
    clearTimeout(t)
  }
}
