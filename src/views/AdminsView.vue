<template>
  <page-loading :loaded="loaded" :error="error">
    <div class="bg-[var(--color-bg-2)] p-2 rounded-md mb-2">
      <a-form :model="form" @submit="search">
        <div class="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-5 gap-4">
          <a-input v-model="form.name" placeholder="姓名" allow-clear />
          <a-input v-model="form.mobile" placeholder="电话" allow-clear />
          <div class="flex gap-x-2">
            <a-button type="secondary" @click="handleAdd">
              <template #icon><IconPlus /> </template>
              创建
            </a-button>
            <a-button type="primary" html-type="submit" @click="search" :loading="fetching">
              <template #icon><IconSearch /> </template>
              查询
            </a-button>
          </div>
        </div>
      </a-form>
    </div>
    <div class="p-2 bg-white rounded-md">
      <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4">
        <div
          v-for="user in list"
          :key="user.id"
          class="bg-[var(--color-bg-2)] p-4 rounded-lg border border-[var(--color-border-1)]"
        >
          <div class="flex">
            <div class="flex-1">
              <div class="text-sm font-bold">{{ user.name }}</div>
              <div class="text-[var(--color-text-2)] text-sm">{{ user.mobile }}</div>
            </div>
            <div>
              <a-button shape="circle" size="mini" @click="handleEdit(user)">
                <template #icon><IconEdit /></template>
              </a-button>
            </div>
          </div>
          <div class="flex items-center gap-x-2 mt-1">
            <a-tag size="small" color="blue">{{ user.roleName }}</a-tag>
            <a-tag size="small" color="red" v-if="user.status !== 1">已禁用</a-tag>
            <a-tag size="small" v-for="hall in user.halls" :key="hall.id" class="text-sm">{{
              hall.name
            }}</a-tag>
          </div>
          <div class="mt-1 text-xs">
            <span class="text-[var(--color-text-2)]">创建：</span>
            <date-time :time="user.createdAt" show-time></date-time>
            <template v-if="user.createdBy">
              <a-divider direction="vertical" /><span>{{ user.createdBy.name }}</span>
            </template>
          </div>
        </div>
      </div>
      <PaginationBar
        :total="total"
        :go-page="goPage"
        :page="variables.page"
        :size="variables.size"
      />
    </div>

    <a-drawer
      :visible="drawerVisible"
      @cancel="closeDrawer"
      @ok="handleSubmit"
      :title="isEdit ? '编辑管理员' : '添加管理员'"
      :width="500"
      :placement="isMdScreen ? 'right' : 'bottom'"
      height="90%"
      unmount-on-close
    >
      <a-form :model="drawerForm" ref="drawerFormRef" @submit="handleSubmit">
        <a-form-item field="name" label="姓名" :rules="[{ required: true, message: '请输入姓名' }]">
          <a-input v-model="drawerForm.name" placeholder="请输入姓名" allow-clear />
        </a-form-item>
        <a-form-item
          field="mobile"
          label="手机号"
          :rules="[
            { required: true, message: '请输入手机号' },
            { match: /^1[3-9]\d{9}$/, message: '请输入正确的手机号' },
          ]"
        >
          <a-input v-model="drawerForm.mobile" placeholder="请输入手机号" allow-clear />
        </a-form-item>
        <a-form-item field="role" label="角色" :rules="[{ required: true, message: '请选择角色' }]">
          <a-select v-model="drawerForm.role" placeholder="请选择角色">
            <a-option value="operator">门店员工</a-option>
            <a-option value="executor" v-permission="['admin']">门店管理员</a-option>
            <a-option value="admin" v-permission="['admin']">超级管理员</a-option>
          </a-select>
        </a-form-item>
        <a-form-item
          v-if="drawerForm.role !== 'admin'"
          field="hallIds"
          label="门店"
          :rules="[{ required: true, message: '请选择门店' }]"
        >
          <a-checkbox-group v-model="drawerForm.hallIds">
            <a-checkbox
              v-for="hall in account.role === 'admin' ? halls : account.halls"
              :key="hall.id"
              :value="hall.id"
            >
              {{ hall.name }}
            </a-checkbox>
          </a-checkbox-group>
        </a-form-item>
        <a-form-item field="status" label="状态" v-if="drawerForm.id">
          <a-radio-group v-model="drawerForm.status">
            <a-radio :value="1">启用</a-radio>
            <a-radio :value="0">禁用</a-radio>
          </a-radio-group>
        </a-form-item>
      </a-form>
      <template #footer>
        <div class="flex justify-end gap-2">
          <div v-permission="['admin', 'executor']">
            <a-popconfirm content="确定将密码重置为【ak123456】吗？" @ok="resetPassword">
              <a-button>重置密码</a-button>
            </a-popconfirm>
          </div>
          <a-button @click="closeDrawer">取消</a-button>
          <a-button type="primary" :loading="submitting" @click="handleSubmit">{{
            isEdit ? '保存' : '创建'
          }}</a-button>
        </div>
      </template>
    </a-drawer>
  </page-loading>
</template>
<script setup>
import gql from 'graphql-tag'
import { ref, reactive, toRaw } from 'vue'
import { usePageRequest } from '@/uses/page-request'
import PageLoading from '@/components/PageLoading.vue'
import PaginationBar from '@/components/PaginationBar.vue'
import DateTime from '@/components/DateTime.vue'
import { Message } from '@arco-design/web-vue'
import client from '@/utils/graphql-client'
import isMdScreen from '../uses/is-md-screen'
import { useHalls } from '@/uses/halls'
import { useAccount } from '@/uses/account'
const { halls } = useHalls()
const { account } = useAccount()

const adminFrag = gql`
  fragment AdminFields on Admin {
    id
    name
    mobile
    status
    role
    roleName
    createdAt
    updatedAt
    lastLoginAt
    lastChangePwdAt
    createdBy {
      id
      name
    }
    halls {
      id
      name
    }
  }
`

const doc = gql`
  query Users($page: Int, $size: Int, $name: String, $mobile: String) {
    admins(page: $page, size: $size, name: $name, mobile: $mobile) {
      total
      list {
        ...AdminFields
      }
    }
  }
  ${adminFrag}
`
const { loaded, error, fetching, list, total, search, form, variables, goPage } =
  usePageRequest(doc)

const drawerVisible = ref(false)
const isEdit = ref(false)
const submitting = ref(false)
const drawerFormRef = ref(null)
const drawerForm = reactive({
  id: '',
  name: '',
  mobile: '',
  status: 1,
  role: 'admin',
  hallIds: [],
})

const createAdminMutation = gql`
  mutation CreateAdmin($name: String!, $mobile: String!, $role: String!, $hallIds: [ID!]) {
    createAdmin(name: $name, mobile: $mobile, role: $role, hallIds: $hallIds) {
      ...AdminFields
    }
  }
  ${adminFrag}
`

const updateAdminMutation = gql`
  mutation UpdateAdmin(
    $id: ID!
    $name: String
    $mobile: String
    $status: Int!
    $role: String!
    $hallIds: [ID!]
  ) {
    updateAdmin(
      id: $id
      name: $name
      mobile: $mobile
      status: $status
      role: $role
      hallIds: $hallIds
    ) {
      ...AdminFields
    }
  }
  ${adminFrag}
`

const handleEdit = (user) => {
  isEdit.value = true
  drawerForm.id = user.id
  drawerForm.name = user.name
  drawerForm.mobile = user.mobile
  drawerForm.status = user.status
  drawerForm.role = user.role
  drawerForm.hallIds = user.halls ? user.halls.map((i) => i.id) : null
  drawerVisible.value = true
}

const handleAdd = () => {
  isEdit.value = false
  drawerForm.id = ''
  drawerVisible.value = true
}

const closeDrawer = () => {
  drawerVisible.value = false
  // drawerFormRef.value?.resetFields()
}

const handleSubmit = async () => {
  submitting.value = true
  const errors = await drawerFormRef.value.validate()
  if (errors) {
    submitting.value = false
    return
  }
  try {
    if (isEdit.value) {
      const res = await client(updateAdminMutation, toRaw(drawerForm))
      Message.success('更新成功')
      const a = res.updateAdmin
      list.value = list.value.map((i) => (i.id === a.id ? { ...i, ...a } : i))
    } else {
      await client(createAdminMutation, toRaw(drawerForm))
      search()
      Message.success('创建成功')
    }
    drawerForm.name = ''
    drawerForm.mobile = ''
    drawerForm.status = 1
    drawerForm.role = ''
    drawerForm.hallIds = []
    closeDrawer()
  } catch (err) {
    Message.error(err.message)
  } finally {
    submitting.value = false
  }
}
const resetPassword = async () => {
  try {
    await client(
      gql`
        mutation ResetAdminPassword($id: ID!) {
          resetAdminPassword(id: $id)
        }
      `,
      { id: drawerForm.id },
    )
    Message.success('重置成功')
  } catch (err) {
    Message.error(err.message)
  }
}
</script>
