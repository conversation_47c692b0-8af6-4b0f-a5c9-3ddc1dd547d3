<template>
  <page-loading :loaded="loaded" :error="error">
    <div class="bg-[var(--color-bg-2)] p-2 rounded-md mb-2">
      <a-form :model="form" @submit="search">
        <div class="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-5 gap-4">
          <a-input v-model="form.name" placeholder="姓名" allow-clear />
          <a-input v-model="form.mobile" placeholder="电话" allow-clear />
          <a-select v-model="form.hallId" placeholder="门店" allow-clear>
            <a-option v-for="hall in account.halls ?? halls" :key="hall.id" :value="hall.id">
              {{ hall.name }}
            </a-option>
          </a-select>
          <a-date-picker
            v-model="form.start"
            style="width: 100%"
            placeholder="开始日期"
            value-format="YYYY-MM-DD"
            allow-clear
          />
          <a-date-picker
            v-model="form.end"
            style="width: 100%"
            placeholder="结束日期"
            value-format="YYYY-MM-DD"
            allow-clear
          />
          <div class="flex gap-x-2">
            <a-button type="primary" html-type="submit" @click="search" :loading="fetching">
              <template #icon><IconSearch /> </template>
              查询
            </a-button>
          </div>
        </div>
      </a-form>
    </div>
    <div class="p-2 bg-white rounded-md">
      <a-table :data="list" :pagination="false" :bordered="false">
        <template #columns>
          <a-table-column title="用户" data-index="user">
            <template #cell="{ record }">
              <user-avatar :user="record.user" />
            </template>
          </a-table-column>
          <a-table-column title="门店" data-index="hall">
            <template #cell="{ record }">
              <span>{{ record.hall?.name || '-' }}</span>
            </template>
          </a-table-column>
          <!-- <a-table-column title="签到日期" data-index="checkinDate">
            <template #cell="{ record }">
              <date-time :time="record.checkinDate" format="YYYY-MM-DD" />
            </template>
          </a-table-column> -->
          <a-table-column title="签到时间" data-index="checkinAt">
            <template #cell="{ record }">
              <date-time :time="record.checkinAt" format="YYYY-MM-DD HH:mm:ss" />
            </template>
          </a-table-column>
          <!-- <a-table-column title="经纬度" data-index="lat">
            <template #cell="{ record }">
              <span>{{ record.lat }}, {{ record.lng }}</span>
            </template>
          </a-table-column> -->
        </template>
      </a-table>
      <PaginationBar
        :total="total"
        :go-page="goPage"
        :page="variables.page"
        :size="variables.size"
      />
    </div>
  </page-loading>
</template>

<script setup>
import gql from 'graphql-tag'
import { usePageRequest } from '@/uses/page-request'
import PageLoading from '@/components/PageLoading.vue'
import PaginationBar from '@/components/PaginationBar.vue'
import DateTime from '@/components/DateTime.vue'
import UserAvatar from '@/components/UserAvatar.vue'
import { userShortFrag } from '@/gql/user'
import { useHalls } from '@/uses/halls'
import { useAccount } from '@/uses/account'
const { halls } = useHalls()
const { account } = useAccount()
const doc = gql`
  query CheckinList(
    $page: Int
    $size: Int
    $hallId: ID
    $userId: ID
    $name: String
    $mobile: String
    $start: String
    $end: String
  ) {
    checkinList(
      page: $page
      size: $size
      hallId: $hallId
      userId: $userId
      name: $name
      mobile: $mobile
      start: $start
      end: $end
    ) {
      total
      list {
        id
        user {
          ...UserFields
        }
        hall {
          id
          name
        }
        checkinDate
        checkinAt
        lat
        lng
      }
    }
  }
  ${userShortFrag}
`
const { loaded, error, fetching, list, total, search, form, variables, goPage } =
  usePageRequest(doc)
</script>
