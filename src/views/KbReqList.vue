<template>
  <page-loading :loaded="loaded" :error="error">
    <div class="bg-[var(--color-bg-1)] p-2 rounded-md mb-2">
      <a-form :model="form" @submit="search">
        <div class="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-6 gap-4">
          <a-input v-model="form.name" placeholder="姓名" allow-clear />
          <a-input v-model="form.mobile" placeholder="电话" allow-clear />
          <a-select
            v-model="form.status"
            placeholder="状态"
            allow-clear
            @clear="form.status = null"
          >
            <a-option :value="0">待审核</a-option>
            <a-option :value="1">已通过</a-option>
            <a-option :value="2">已拒绝</a-option>
          </a-select>
          <a-select v-model="form.hallId" placeholder="门店" allow-clear>
            <a-option v-for="hall in account.halls ?? halls" :key="hall.id" :value="hall.id">
              {{ hall.name }}
            </a-option>
          </a-select>
          <div class="flex gap-x-2">
            <a-button type="primary" html-type="submit" @click="search" :loading="fetching">
              <template #icon><IconSearch /> </template>
              查询
            </a-button>
          </div>
        </div>
      </a-form>
    </div>
    <div class="p-2 bg-white rounded-md">
      <a-table
        :data="list"
        :hoverable="false"
        stripe
        :pagination="false"
        :bordered="false"
        :scroll="{ x: 800 }"
        :scrollbar="false"
      >
        <template #columns>
          <a-table-column title="用户" data-index="user" :width="125">
            <template #cell="{ record }">
              <user-avatar :user="record.user" />
              <!-- <span @click="xx">xx</span> -->
            </template>
          </a-table-column>
          <a-table-column title="K币" data-index="kb" :width="70">
            <template #cell="{ record }">
              <div>
                <span class="font-semibold text-[rgb(var(--primary-6))] text-base"
                  >{{ record.kb }}
                </span>
                <span class="ml-1 mr-2">K币</span>
              </div>
              <div class="text-sm">
                <a-tag size="mini">{{ record.hall.name }}</a-tag>
              </div>
              <div>
                <a-tag v-if="record.status === 0" color="orange" size="mini">待审核</a-tag>
                <a-tag v-else-if="record.status === 1" color="green" size="mini">已通过</a-tag>
                <a-tag v-else-if="record.status === 2" color="red" size="mini">已拒绝</a-tag>
                <a-tag v-else color="gray" size="mini">未知</a-tag>
              </div>
            </template>
          </a-table-column>
          <a-table-column title="来源 | 理由" data-index="source" :width="180">
            <template #cell="{ record }">
              <a-tag color="arcoblue">{{ record.source }}</a-tag>
              <div>{{ record.reason || '-' }}</div>
              <div v-if="record.comment" class="text-xs text-[var(--color-text-2)]">
                {{ record.comment }}
              </div>
              <div v-if="record.images?.length" class="text-sm">
                <icon-image /> <icon-close class="text-xs text-[var(--color-text-2)]" />
                {{ record.images?.length }}
              </div>
            </template>
          </a-table-column>
          <a-table-column title="提交/审核" data-index="createdAt" :width="140">
            <template #cell="{ record }">
              <div class="text-xs">
                <div class="text-xs">提交：</div>
                <span>{{ record.createdBy?.name }}</span>
                <date-time
                  :time="record.createdAt"
                  pretty
                  show-time
                  class="text-[var(--color-text-2)] ml-2"
                />
              </div>
              <div class="text-xs" v-if="record.status !== 0">
                <div class="text-xs">审核：</div>
                <span>{{ record.approvedBy?.name || '-' }}</span>
                <date-time
                  :time="record.approvedAt"
                  pretty
                  show-time
                  class="text-[var(--color-text-2)] ml-2"
                />
              </div>
            </template>
          </a-table-column>
          <a-table-column title="操作" data-index="action" :width="80" fixed="right">
            <template #cell="{ record }">
              <div class="flex flex-col gap-1">
                <div>
                  <a-button size="mini" type="primary" @click="openApprovalModal(record, false)">
                    <template #icon><icon-eye></icon-eye></template>查看
                  </a-button>
                </div>
                <div>
                  <a-button
                    size="mini"
                    @click="openApprovalModal(record, true)"
                    :disabled="record.status !== 0"
                    type="primary"
                  >
                    <template #icon><icon-stamp></icon-stamp></template>审核
                  </a-button>
                </div>
              </div>
            </template>
          </a-table-column>
        </template>
      </a-table>
      <PaginationBar
        :total="total"
        :go-page="goPage"
        :page="variables.page"
        :size="variables.size"
      />
    </div>
  </page-loading>

  <!-- 审核 Modal -->
  <a-modal
    v-model:visible="approvalModalVisible"
    :title="showApproveButton ? 'K币 审核' : '查看 K币 提交'"
    width="500px"
    :modal-style="{ maxWidth: '100vw' }"
    @cancel="closeApprovalModal"
    :footer="false"
  >
    <div v-if="currentRecord">
      <!-- 用户信息 -->
      <div class="">
        <div>
          <a-descriptions :column="1" size="small">
            <a-descriptions-item label="客户">
              <user-avatar :user="currentRecord.user" :show-addition="false" />
            </a-descriptions-item>
            <a-descriptions-item label="K币">
              <span class="font-semibold text-[rgb(var(--primary-6))]">
                {{ currentRecord.kb }}
              </span>
              kb
            </a-descriptions-item>
            <a-descriptions-item label="来源">{{ currentRecord.source }}</a-descriptions-item>
            <a-descriptions-item label="理由">{{ currentRecord.reason }}</a-descriptions-item>
            <a-descriptions-item label="备注">{{ currentRecord.comment }}</a-descriptions-item>
            <a-descriptions-item label="图片">
              <a-image-preview-group infinite>
                <a-space>
                  <a-image
                    class="cursor-pointer"
                    v-for="img in currentRecord.images"
                    :key="img"
                    :src="img + '-cover.jpg'"
                    height="80"
                  />
                </a-space>
              </a-image-preview-group>
            </a-descriptions-item>
            <a-descriptions-item label="提交">
              <span class="mr-3">{{ currentRecord.createdBy.name }}</span>
              <date-time
                :time="currentRecord.createdAt"
                show-time
                pretty
                class="text-[var(--color-text-2)]"
              ></date-time>
            </a-descriptions-item>
            <a-descriptions-item label="状态">
              <a-tag v-if="currentRecord.status === 0" color="orange" size="mini">待审核</a-tag>
              <a-tag v-else-if="currentRecord.status === 1" color="green" size="mini">已通过</a-tag>
              <a-tag v-else-if="currentRecord.status === 2" color="red" size="mini">已拒绝</a-tag>
              <a-tag v-else color="gray" size="mini">未知</a-tag>
            </a-descriptions-item>
            <a-descriptions-item label="审核" v-if="currentRecord.status !== 0">
              <div>
                <span class="mr-3">{{ currentRecord.approvedBy.name }}</span>
                <date-time
                  :time="currentRecord.approvedAt"
                  show-time
                  pretty
                  class="text-[var(--color-text-2)]"
                ></date-time>
              </div>
              <div v-if="currentRecord.approveComment">{{ currentRecord.approveComment }}</div>
            </a-descriptions-item>
          </a-descriptions>
        </div>
      </div>

      <!-- 审核表单 -->
      <a-divider v-if="showApproveButton" />
      <a-form :model="approvalForm" layout="vertical" class="mt-2" v-if="showApproveButton">
        <a-form-item field="approveComment" label="审核备注">
          <a-textarea
            v-model="approvalForm.approveComment"
            placeholder="请输入审核备注..."
            :max-length="200"
            show-word-limit
            :rows="4"
          />
        </a-form-item>

        <!-- 操作按钮 -->
        <div class="flex gap-3 justify-end">
          <a-button @click="closeApprovalModal">取消</a-button>
          <a-button status="danger" @click="handleApproval(2)" :loading="approvalSubmitting">
            不通过
          </a-button>
          <a-button type="primary" @click="handleApproval(1)" :loading="approvalSubmitting">
            通过
          </a-button>
        </div>
      </a-form>
    </div>
  </a-modal>
</template>

<script setup>
import gql from 'graphql-tag'
import { ref, reactive } from 'vue'
import { usePageRequest } from '@/uses/page-request'
import PageLoading from '@/components/PageLoading.vue'
import PaginationBar from '@/components/PaginationBar.vue'
import DateTime from '@/components/DateTime.vue'
import UserAvatar from '@/components/UserAvatar.vue'
import { userShortFrag } from '@/gql/user'
import { useHalls } from '@/uses/halls'
import { IconSearch } from '@arco-design/web-vue/es/icon'
import { Message } from '@arco-design/web-vue'
import client from '@/utils/graphql-client'
import { useAccount } from '@/uses/account'
const { account } = useAccount()

const { halls } = useHalls()

const doc = gql`
  query UserKbReqList(
    $page: Int
    $size: Int
    $hallId: ID
    $userId: ID
    $status: Int
    $name: String
    $mobile: String
  ) {
    userKbReqList(
      page: $page
      size: $size
      hallId: $hallId
      userId: $userId
      status: $status
      name: $name
      mobile: $mobile
    ) {
      total
      list {
        id
        user {
          ...UserFields
        }
        hall {
          id
          name
        }
        kb
        source
        reason
        comment
        images
        status
        createdAt
        createdBy {
          id
          name
        }
        approvedAt
        approvedBy {
          id
          name
        }
        approveComment
      }
    }
  }
  ${userShortFrag}
`

const { loaded, error, fetching, list, total, search, form, variables, goPage } =
  usePageRequest(doc)

// 审核相关状态
const showApproveButton = ref(false)
const approvalModalVisible = ref(false)
const currentRecord = ref(null)
const approvalSubmitting = ref(false)
const approvalForm = reactive({
  approveComment: '',
})

// 打开审核 Modal
const openApprovalModal = (record, showButton) => {
  currentRecord.value = record
  approvalModalVisible.value = true
  showApproveButton.value = showButton
}
// 关闭审核 Modal
const closeApprovalModal = () => {
  approvalModalVisible.value = false
  currentRecord.value = null
}

// 处理审核操作
const handleApproval = async (status) => {
  approvalSubmitting.value = true
  try {
    if (status === 2 && !approvalForm.approveComment.trim()) {
      Message.warning('请输入审核备注')
      return
    }
    const mutation = gql`
      mutation ApproveKbReq($id: ID!, $status: Int!, $approveComment: String!) {
        approveUserKbReq(id: $id, status: $status, approveComment: $approveComment) {
          id
          status
          approvedAt
          approvedBy {
            id
            name
          }
          approveComment
        }
      }
    `

    const result = await client(mutation, {
      id: currentRecord.value.id,
      status,
      approveComment: approvalForm.approveComment,
    })

    // 更新列表中的记录
    const updatedRecord = result.approveUserKbReq
    const index = list.value.findIndex((item) => item.id === updatedRecord.id)
    if (index !== -1) {
      list.value[index] = {
        ...list.value[index],
        ...updatedRecord,
      }
    }

    Message.success(
      status === 1
        ? '审核通过'
        : status === 2
          ? '审核不通过'
          : status === 0
            ? '已重置未未审核'
            : '未定义',
    )
    closeApprovalModal()
    approvalForm.approveComment = ''
  } catch (error) {
    Message.error(error.message || '审核失败')
  } finally {
    approvalSubmitting.value = false
  }
}
</script>
