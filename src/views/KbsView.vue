<template>
  <page-loading :loaded="loaded" :error="error">
    <div class="bg-[var(--color-bg-2)] p-2 rounded-md mb-2">
      <a-form :model="form" @submit="search">
        <div class="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-6 gap-4">
          <a-input v-model="form.name" placeholder="姓名" allow-clear />
          <a-input v-model="form.mobile" placeholder="电话" allow-clear />
          <a-select v-model="form.hallId" placeholder="门店" allow-clear>
            <a-option v-for="hall in halls" :key="hall.id" :value="hall.id">
              {{ hall.name }}
            </a-option>
          </a-select>
          <a-select v-model="form.type" placeholder="类型" allow-clear>
            <a-option value="+">增加</a-option>
            <a-option value="-">消费</a-option>
          </a-select>
          <div class="flex gap-x-2">
            <a-button type="primary" html-type="submit" @click="search" :loading="fetching">
              <template #icon><icon-search /> </template>
              查询
            </a-button>
          </div>
        </div>
      </a-form>
    </div>
    <div class="p-2 bg-white rounded-md">
      <a-table
        :data="list"
        row-key="id"
        :hoverable="false"
        stripe
        :pagination="false"
        :bordered="false"
        :scroll="{ x: 600 }"
        :scrollbar="false"
      >
        <template #columns>
          <a-table-column title="用户" data-index="user" :width="160">
            <template #cell="{ record }">
              <div class="flex items-center gap-2">
                <user-avatar :user="record.user" />
              </div>
            </template>
          </a-table-column>
          <a-table-column title="KB值" data-index="kb" :width="90">
            <template #cell="{ record }">
              <div :class="record.kb > 0 ? 'text-green-500' : 'text-red-500'">
                {{ record.kb > 0 ? '+' : '' }}{{ record.kb }}
              </div>
            </template>
          </a-table-column>
          <a-table-column title="门店" data-index="hall" :width="80">
            <template #cell="{ record }">
              <div class="text-sm">{{ record.hall?.name || '-' }}</div>
            </template>
          </a-table-column>
          <a-table-column title="途径|来源" data-index="source" :width="120">
            <template #cell="{ record }">
              <a-tag size="mini" color="arcoblue">{{ record.source }}</a-tag>
              <div class="text-xs text-[var(--color-text-2)] mt-1">
                {{ record.reason || '' }}
              </div>
            </template>
          </a-table-column>
          <a-table-column title="操作" data-index="createdAt" :width="160">
            <template #cell="{ record }">
              <div>{{ record.operator?.name }}</div>
              <date-time
                :time="record.createdAt"
                format="YYYY-MM-DD HH:mm:ss"
                class="text-xs"
              ></date-time>
            </template>
          </a-table-column>
        </template>
      </a-table>
      <PaginationBar
        :total="total"
        :go-page="goPage"
        :page="variables.page"
        :size="variables.size"
      />
    </div>
  </page-loading>
</template>
<script setup>
import gql from 'graphql-tag'
import { usePageRequest } from '@/uses/page-request'
import PageLoading from '@/components/PageLoading.vue'
import PaginationBar from '@/components/PaginationBar.vue'
import DateTime from '@/components/DateTime.vue'
import UserAvatar from '@/components/UserAvatar.vue'
import { userShortFrag } from '@/gql/user'
import { useHalls } from '@/uses/halls'
import { IconSearch } from '@arco-design/web-vue/es/icon'

const { halls } = useHalls()

const doc = gql`
  query UserKbLogs(
    $page: Int
    $size: Int
    $hallId: ID
    $userId: ID
    $name: String
    $mobile: String
    $type: String
  ) {
    userKbLogs(
      page: $page
      size: $size
      hallId: $hallId
      userId: $userId
      name: $name
      mobile: $mobile
      type: $type
    ) {
      total
      list {
        id
        kb
        user {
          ...UserFields
        }
        hall {
          id
          name
        }
        source
        reason
        createdAt
        operator {
          id
          name
        }
      }
    }
  }
  ${userShortFrag}
`
const { loaded, error, fetching, list, total, search, form, variables, goPage } =
  usePageRequest(doc)
</script>
