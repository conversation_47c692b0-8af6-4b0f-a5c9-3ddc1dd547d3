<template>
  <page-loading :loaded="loaded" :error="error">
    <div class="p-2 bg-white rounded-md">
      <a-tabs v-if="halls?.length">
        <template #extra>
          <a-button type="primary" @click="openDrawer()" v-permission="['admin', 'executor']">
            <template #icon><IconPlus /></template>
            添加球杆柜
          </a-button>
        </template>
        <a-tab-pane v-for="hall in halls" :key="hall.id" :title="hall.name">
          <div
            class="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-5 gap-4"
          >
            <div
              class="flex flex-col py-2 px-4 rounded-md border border-[var(--color-border-1)]"
              v-for="locker in hall.lockers"
              :key="locker.id"
            >
              <div class="border-b pb-2 border-[var(--color-border-1)] flex">
                <div class="flex-1">
                  <div class="font-bold text-base">{{ locker.lockerNo }}</div>
                  <div v-if="locker.remark" class="text-xs text-[var(--color-text-3)]">
                    <icon-info-circle />
                    备注: {{ locker.remark }}
                  </div>
                </div>
                <div class="flex">
                  <a-button
                    type="text"
                    @click="openDrawer(locker, hall)"
                    v-permission="['admin', 'executor']"
                  >
                    <template #icon><IconEdit /></template>
                  </a-button>
                  <div v-permission="['admin', 'executor']">
                    <a-popconfirm content="确定要删除这个储物柜吗？" @ok="handleDelete(locker)">
                      <a-button type="text" status="danger">
                        <template #icon><IconDelete /></template>
                      </a-button>
                    </a-popconfirm>
                  </div>
                </div>
              </div>
              <div class="pt-2 flex-1">
                <div class="font-semibold text-sm mb-1 text-[var(--color-text-2)]">当前客户</div>
                <div>
                  <user-avatar v-if="locker.userLocker" :user="locker.userLocker.user" />
                  <a-tag v-else color="orangered">空置</a-tag>
                </div>
              </div>
              <div>
                <div class="text-xs mt-2 text-[var(--color-text-3)]">
                  <div v-if="locker.updatedAt">
                    <span>更新：</span>
                    <date-time :time="locker.updatedAt" show-time />
                    <span>{{ locker.updatedBy.name }}</span>
                  </div>
                  <div v-else>
                    <span>创建：</span>
                    <date-time :time="locker.createdAt" show-time />
                    <span>{{ locker.createdBy.name }}</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </a-tab-pane>
      </a-tabs>
    </div>

    <a-drawer
      :visible="drawerVisible"
      :title="currentLocker ? '编辑储物柜' : '添加储物柜'"
      @cancel="closeDrawer"
      @ok="handleSubmit"
      :width="500"
      :placement="isMdScreen ? 'right' : 'bottom'"
      height="90%"
    >
      <a-form :model="form" layout="vertical" ref="drawerFormRef">
        <a-form-item
          field="hallId"
          label="所属场馆"
          :rules="[{ required: true, message: '请选择所属场馆' }]"
        >
          <a-select v-model="form.hallId" placeholder="请选择所属场馆">
            <a-option v-for="hall in halls" :key="hall.id" :value="hall.id">
              {{ hall.name }}
            </a-option>
          </a-select>
        </a-form-item>
        <a-form-item
          field="lockerNo"
          label="储物柜编号"
          :rules="[{ required: true, message: '请输入储物柜编号' }]"
        >
          <a-input v-model="form.lockerNo" placeholder="请输入储物柜编号" />
        </a-form-item>
        <a-form-item field="remark" label="备注">
          <a-textarea v-model="form.remark" placeholder="请输入备注" />
        </a-form-item>
        <a-form-item field="orderNo" label="排序号">
          <a-input-number v-model="form.orderNo" placeholder="请输入排序号" :min="1" />
        </a-form-item>
      </a-form>
    </a-drawer>
  </page-loading>
</template>
<script setup>
import gql from 'graphql-tag'
import { useRequest } from '@/uses/request'
import { ref } from 'vue'
import { Message } from '@arco-design/web-vue'
import client from '@/utils/graphql-client'
import isMdScreen from '../uses/is-md-screen'
import UserAvatar from '@/components/UserAvatar.vue'
import { userShortFrag } from '@/gql/user'

const doc = gql`
  query Halls {
    halls {
      id
      name
      address
      orderNo
      lockers {
        id
        lockerNo
        remark
        orderNo
        status
        createdAt
        createdBy {
          id
          name
        }
        updatedAt
        updatedBy {
          id
          name
        }
        userLocker {
          id
          user {
            ...UserFields
          }
          createdAt
          createdBy {
            id
            name
          }
        }
      }
    }
  }
  ${userShortFrag}
`
const {
  loaded,
  error,
  data: { halls },
  fetch,
} = useRequest(doc)
const drawerVisible = ref(false)
const form = ref({
  hallId: '',
  lockerNo: '',
  remark: '',
  orderNo: 100,
})
const drawerFormRef = ref(null)
const currentLocker = ref(null)
const currentHall = ref(null)
const openDrawer = (locker = null, hall = null) => {
  if (locker) {
    currentLocker.value = locker
    currentHall.value = hall
    form.value = {
      id: locker.id,
      hallId: hall.id,
      lockerNo: locker.lockerNo,
      remark: locker.remark,
      orderNo: locker.orderNo,
    }
  } else {
    currentLocker.value = null
    currentHall.value = null
    form.value = {
      hallId: '',
      lockerNo: '',
      remark: '',
      orderNo: 100,
    }
  }
  drawerVisible.value = true
}

const closeDrawer = () => {
  drawerVisible.value = false
  form.value = {
    hallId: '',
    lockerNo: '',
    remark: '',
    orderNo: 100,
  }
  currentLocker.value = null
  currentHall.value = null
}

const handleSubmit = async () => {
  const errors = await drawerFormRef.value.validate()
  if (errors) {
    return
  }
  try {
    if (form.value.id) {
      await client(
        gql`
          mutation UpdateLocker(
            $id: ID!
            $hallId: ID!
            $lockerNo: String!
            $remark: String
            $orderNo: Int
          ) {
            updateLocker(
              id: $id
              hallId: $hallId
              lockerNo: $lockerNo
              remark: $remark
              orderNo: $orderNo
            )
          }
        `,
        form.value,
      )
      Message.success('更新成功')
    } else {
      await client(
        gql`
          mutation AddLocker($hallId: ID!, $lockerNo: String!, $remark: String, $orderNo: Int) {
            addLocker(hallId: $hallId, lockerNo: $lockerNo, remark: $remark, orderNo: $orderNo)
          }
        `,
        form.value,
      )
      Message.success('创建成功')
    }
    closeDrawer()
    fetch()
  } catch (err) {
    Message.error(err.message || '操作失败')
  }
}

const handleDelete = async (locker) => {
  try {
    await client(
      gql`
        mutation DeleteLocker($id: ID!) {
          deleteLocker(id: $id)
        }
      `,
      { id: locker.id },
    )
    Message.success('删除成功')
    fetch()
  } catch (err) {
    Message.error(err.message || '删除失败')
  }
}
</script>
<style scoped>
:deep(.arco-card) {
  display: flex;
  flex-direction: column;
}
:deep(.x) .arco-card-body {
  flex-grow: 1;
  display: flex;
  flex-direction: column;
}
</style>
