<template>
  <page-loading :loaded="loaded" :error="error">
    <div class="bg-[var(--color-bg-2)] p-2 rounded-md mb-2">
      <a-form :model="form" @submit="search">
        <div class="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-5 gap-4">
          <a-input v-model="form.name" placeholder="姓名" allow-clear />
          <a-input v-model="form.mobile" placeholder="电话" allow-clear />
          <a-select v-model="form.type" placeholder="类型" allow-clear>
            <a-option value="+">增加</a-option>
            <a-option value="-">消费</a-option>
          </a-select>
          <div class="flex gap-x-2">
            <a-button type="primary" html-type="submit" @click="search" :loading="fetching">
              <template #icon><icon-search /> </template>
              查询
            </a-button>
          </div>
        </div>
      </a-form>
    </div>
    <div class="p-2 bg-white rounded-md">
      <a-table
        :data="list"
        :hoverable="false"
        stripe
        :pagination="false"
        :bordered="false"
        :scroll="{ x: 600 }"
        :scrollbar="false"
      >
        <template #columns>
          <a-table-column title="用户" data-index="user" :width="110">
            <template #cell="{ record }">
              <div class="flex items-center gap-2">
                <user-avatar :user="record.user" />
              </div>
            </template>
          </a-table-column>
          <a-table-column title="积分" data-index="points" :width="60">
            <template #cell="{ record }">
              <div :class="record.points > 0 ? 'text-green-500' : 'text-red-500'">
                {{ record.points > 0 ? '+' : '' }}{{ record.points }}
              </div>
            </template>
          </a-table-column>
          <a-table-column title="途径|来源" data-index="source" :width="110">
            <template #cell="{ record }">
              <a-tag size="mini" color="arcoblue">{{ record.source }}</a-tag>
              <div class="text-xs text-[var(--color-text-2)] mt-1">
                {{ record.reason || '' }}
              </div>
            </template>
          </a-table-column>
          <a-table-column title="操作" data-index="createdAt" :width="130">
            <template #cell="{ record }">
              <div>{{ record.operator?.name }}</div>
              <date-time
                :time="record.createdAt"
                format="YYYY-MM-DD HH:mm:ss"
                class="text-xs"
              ></date-time>
            </template>
          </a-table-column>
        </template>
      </a-table>
      <PaginationBar
        :total="total"
        :go-page="goPage"
        :page="variables.page"
        :size="variables.size"
      />
    </div>
  </page-loading>
</template>
<script setup>
import gql from 'graphql-tag'
import { usePageRequest } from '@/uses/page-request'
import PageLoading from '@/components/PageLoading.vue'
import PaginationBar from '@/components/PaginationBar.vue'
import DateTime from '@/components/DateTime.vue'
import UserAvatar from '@/components/UserAvatar.vue'
import { userShortFrag } from '@/gql/user'

const doc = gql`
  query UserPointLogs($page: Int, $size: Int, $name: String, $mobile: String, $type: String) {
    userPointLogs(page: $page, size: $size, name: $name, mobile: $mobile, type: $type) {
      total
      list {
        id
        points
        source
        reason
        operator {
          id
          name
        }
        createdAt
        user {
          ...UserFields
        }
      }
    }
  }
  ${userShortFrag}
`
const { loaded, error, fetching, list, total, search, form, variables, goPage } =
  usePageRequest(doc)
</script>
