<template>
  <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
    <div class="bg-white px-4 pt-2 pb-4 rounded-md">
      <div class="flex pb-2 border-b border-[var(--color-border-1)]">
        <div class="flex-1 text-base font-bold">K币排行榜</div>
        <div>
          <a-button type="text" @click="downloadExcel">
            <template #icon><icon-download /></template>
          </a-button>
          <a-button type="text" @click="fetchKbRank(false)">
            <template #icon><icon-refresh /></template>
          </a-button>
        </div>
      </div>
      <div class="pt-3">
        <div class="mb-2">
          <a-tabs
            type="rounded"
            position="top"
            hide-content
            size="small"
            v-model:active-key="hallId"
          >
            <a-tab-pane v-for="h in halls" :key="h.id" :title="h.name"></a-tab-pane>
          </a-tabs>
        </div>
        <div class="flex-1">
          <div class="space-y-2">
            <div
              v-for="rank in kbRanks"
              :key="rank.id"
              class="flex items-center p-2 rounded-md hover:bg-[var(--color-fill-2)]"
            >
              <div class="w-10 text-center font-medium">
                {{ rank.rank }}
              </div>
              <div class="mx-2 flex-1">
                <user-avatar :user="rank.user" />
              </div>
              <div class="text-lg font-medium text-[var(--color-text-1)]">
                {{ rank.kb }}
              </div>
            </div>
            <div v-if="!kbRanks.length" class="text-center py-8 text-[var(--color-text-3)]">
              暂无排行数据
            </div>
            <div v-else>
              <div
                v-if="!kbRanksNoMore"
                class="cursor-pointer py-2 text-center"
                @click="fetchKbRank(true)"
              >
                点击记载更多
              </div>
              <div v-else class="py-2 text-center text-[var(--color-text-3)]">没有更多了</div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <div class="bg-white px-4 pt-2 pb-4 rounded-md">
      <div class="flex pb-2 border-b border-[var(--color-border-1)]">
        <div class="flex-1 text-base font-bold">积分排行榜</div>
        <div>
          <a-button type="text" @click="fetchPointsRank(false)">
            <template #icon><icon-refresh /></template>
          </a-button>
        </div>
      </div>
      <div>
        <div class="space-y-2">
          <div
            v-for="rank in pointsRanks"
            :key="rank.id"
            class="flex items-center p-2 over:bg-[var(--color-fill-2)]"
          >
            <div class="w-10 text-center font-medium">
              {{ rank.rank }}
            </div>
            <div class="mx-2 flex-1">
              <user-avatar :user="rank.user" />
            </div>
            <div class="text-lg font-medium text-[var(--color-text-1)]">
              {{ rank.points }}
            </div>
          </div>
          <div v-if="!pointsRanks.length" class="text-center py-8 text-[var(--color-text-3)]">
            暂无排行数据
          </div>
          <div v-else>
            <div
              v-if="!pointsRanksNoMore"
              class="cursor-pointer py-2 text-center"
              @click="fetchPointsRank(true)"
            >
              点击记载更多
            </div>
            <div v-else class="py-2 text-center text-[var(--color-text-3)]">没有更多了</div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import gql from 'graphql-tag'
import { ref, watch } from 'vue'
import client from '@/utils/graphql-client'
import { userShortFrag } from '@/gql/user'
import { useHalls } from '@/uses/halls'
const { halls } = useHalls()
import UserAvatar from '@/components/UserAvatar.vue'

const pointsRanksError = ref(null)
const pointsRanks = ref([])
const pointsRanksNoMore = ref(false)
function fetchPointsRank(fetchMore) {
  const lastPoint = fetchMore ? pointsRanks.value[pointsRanks.value.length - 1].points : null
  const doc = gql`
    query PointsRanks($lastPoint: Int, $size: Int) {
      pointsRanks(lastPoint: $lastPoint, size: $size) {
        id
        points
        rank
        user {
          ...UserFields
        }
      }
    }
    ${userShortFrag}
  `
  client(doc, { lastPoint, size: 30 })
    .then((res) => {
      pointsRanks.value = !fetchMore ? res.pointsRanks : [...pointsRanks.value, ...res.pointsRanks]
      pointsRanksNoMore.value = res.pointsRanks.length < 30
    })
    .catch((err) => {
      pointsRanksError.value = err
    })
}
fetchPointsRank()

const kbRanks = ref([])
const kbRanksNoMore = ref(false)
const kbRanksError = ref(null)
const hallId = ref('6jpxmb5lme')
function fetchKbRank(fetchMore) {
  const lastKb = fetchMore ? kbRanks.value[kbRanks.value.length - 1].kb : null
  const doc = gql`
    query KbRanks($lastKb: Int, $size: Int, $hallId: ID!) {
      kbRanks(lastKb: $lastKb, size: $size, hallId: $hallId) {
        id
        kb
        rank
        user {
          ...UserFields
        }
      }
    }
    ${userShortFrag}
  `
  client(doc, { lastKb, size: 30, hallId: hallId.value })
    .then((res) => {
      kbRanks.value = !fetchMore ? res.kbRanks : [...kbRanks.value, ...res.kbRanks]
      kbRanksNoMore.value = res.kbRanks.length < 30
      kbRanksError.value = null
    })
    .catch((err) => {
      kbRanksError.value = err
    })
}
fetchKbRank()
watch(hallId, () => {
  fetchKbRank(false)
})
function downloadExcel() {
  const url = '/excel/ranks/kb?hallId=' + hallId.value
  window.location.href = url
}
</script>

<style scoped>
.space-y-8 > :not([hidden]) ~ :not([hidden]) {
  margin-top: 2rem;
}
</style>
