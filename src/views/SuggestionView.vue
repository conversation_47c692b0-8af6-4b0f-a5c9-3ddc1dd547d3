<template>
  <page-loading :loaded="loaded" :error="error">
    <div class="bg-[var(--color-bg-2)] p-2 rounded-md mb-2">
      <a-form :model="form" @submit="search">
        <div class="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-5 gap-4">
          <a-input v-model="form.name" placeholder="姓名" allow-clear />
          <a-input v-model="form.mobile" placeholder="电话" allow-clear />
          <a-select v-model="form.hallId" placeholder="门店" allow-clear>
            <a-option v-for="hall in halls" :key="hall.id" :value="hall.id">
              {{ hall.name }}
            </a-option>
          </a-select>
          <div class="flex gap-x-2">
            <a-button type="primary" html-type="submit" @click="search" :loading="fetching">
              <template #icon><IconSearch /> </template>
              查询
            </a-button>
          </div>
        </div>
      </a-form>
    </div>
    <div class="p-2 bg-white rounded-md">
      <a-table :data="list" :pagination="false" :bordered="false">
        <template #columns>
          <a-table-column title="用户" data-index="user">
            <template #cell="{ record }">
              <user-avatar :user="record.user" />
            </template>
          </a-table-column>
          <a-table-column title="门店" data-index="hall">
            <template #cell="{ record }">
              <span>{{ record.hall?.name || '-' }}</span>
            </template>
          </a-table-column>
          <a-table-column title="建议内容" data-index="suggestion">
            <template #cell="{ record }">
              <span>{{ record.suggestion }}</span>
            </template>
          </a-table-column>
          <a-table-column title="提交时间" data-index="createdAt">
            <template #cell="{ record }">
              <date-time :time="record.createdAt" format="YYYY-MM-DD HH:mm:ss" />
            </template>
          </a-table-column>
        </template>
      </a-table>
      <PaginationBar
        :total="total"
        :go-page="goPage"
        :page="variables.page"
        :size="variables.size"
      />
    </div>
  </page-loading>
</template>

<script setup>
import gql from 'graphql-tag'
import { usePageRequest } from '@/uses/page-request'
import PageLoading from '@/components/PageLoading.vue'
import PaginationBar from '@/components/PaginationBar.vue'
import DateTime from '@/components/DateTime.vue'
import { ref } from 'vue'
import { Message } from '@arco-design/web-vue'
import client from '@/utils/graphql-client'
import UserAvatar from '@/components/UserAvatar.vue'
import { userShortFrag } from '@/gql/user'

const doc = gql`
  query Suggestions(
    $page: Int
    $size: Int
    $hallId: ID
    $userId: ID
    $name: String
    $mobile: String
  ) {
    suggestions(
      page: $page
      size: $size
      hallId: $hallId
      userId: $userId
      name: $name
      mobile: $mobile
    ) {
      total
      list {
        id
        user {
          ...UserFields
        }
        hall {
          id
          name
        }
        suggestion
        createdAt
      }
    }
  }
  ${userShortFrag}
`

const hallsQuery = gql`
  query Halls {
    halls {
      id
      name
    }
  }
`

const halls = ref([])

const fetchHalls = async () => {
  try {
    const res = await client(hallsQuery)
    halls.value = res.halls
  } catch (err) {
    Message.error(err.message || '获取门店列表失败')
  }
}
fetchHalls()

const { loaded, error, fetching, list, total, search, form, variables, goPage } =
  usePageRequest(doc)
</script>
