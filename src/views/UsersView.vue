<template>
  <page-loading :loaded="loaded" :error="error">
    <div class="bg-[var(--color-bg-2)] p-4 rounded-md mb-4">
      <a-form :model="form" @submit="search">
        <div class="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-5 gap-4">
          <a-input v-model="form.name" placeholder="姓名" allow-clear />
          <a-input v-model="form.mobile" placeholder="电话" allow-clear />
          <a-select v-model="form.level" placeholder="档位" allow-clear>
            <a-option :value="1">1档</a-option>
            <a-option :value="2">2档</a-option>
            <a-option :value="3">3档</a-option>
            <a-option :value="4">4档</a-option>
            <a-option :value="5">5档</a-option>
            <a-option :value="6">6档</a-option>
            <a-option :value="7">7档</a-option>
            <a-option :value="8">8档</a-option>
            <a-option :value="9">9档</a-option>
            <a-option :value="10">10档</a-option>
          </a-select>
          <a-select v-model="form.hobbies" placeholder="爱好" multiple allow-clear>
            <a-option value="hobby1">斯诺克</a-option>
            <a-option value="hobby2">中式</a-option>
          </a-select>
          <div class="flex gap-x-2">
            <a-button type="secondary" @click="openDrawer()">
              <template #icon><IconPlus /> </template>
              添加
            </a-button>
            <a-button type="primary" html-type="submit" @click="search" :loading="fetching">
              <template #icon><IconSearch /> </template>
              查询
            </a-button>
          </div>
        </div>
      </a-form>
    </div>
    <div>
      <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
        <div
          v-for="user in list"
          :key="user.id"
          class="flex flex-col bg-[var(--color-bg-2)] px-4 pt-4 rounded-lg"
        >
          <user-avatar :user="user" show-addition show-points></user-avatar>
        </div>
      </div>
      <PaginationBar
        :total="total"
        :go-page="goPage"
        :page="variables.page"
        :size="variables.size"
      />
    </div>
    <a-drawer
      v-model:visible="drawerVisible"
      title="添加用户"
      :width="500"
      :placement="isMdScreen ? 'right' : 'bottom'"
      height="90%"
      :footer="false"
    >
      <a-form :model="userForm" ref="drawerFormRef" @submit="handleSubmit" layout="vertical">
        <a-form-item
          field="markName"
          label="备注名"
          :rules="[{ required: true, message: '请输入' }]"
        >
          <a-input v-model="userForm.markName" placeholder="请输入备注姓名" allow-clear />
        </a-form-item>
        <a-form-item
          field="mobile"
          label="电话"
          validate-trigger="blur"
          :rules="[{ required: true, message: '请输入' }]"
        >
          <a-input v-model="userForm.mobile" placeholder="请输入电话" allow-clear />
        </a-form-item>
        <a-form-item field="gender" label="性别">
          <a-radio-group v-model="userForm.gender">
            <a-radio :value="1">男</a-radio>
            <a-radio :value="2">女</a-radio>
            <a-radio :value="0">未知</a-radio>
          </a-radio-group>
        </a-form-item>
        <a-form-item field="level" label="档位">
          <a-select v-model="userForm.level">
            <a-option :value="i - 1" v-for="i in 11" :key="i">{{ i - 1 }}档</a-option>
          </a-select>
        </a-form-item>
        <a-form-item>
          <a-button type="primary" html-type="submit" long>添加用户</a-button>
        </a-form-item>
      </a-form>
    </a-drawer>
  </page-loading>
</template>
<script setup>
import gql from 'graphql-tag'
import { usePageRequest } from '@/uses/page-request'
import PageLoading from '@/components/PageLoading.vue'
import PaginationBar from '@/components/PaginationBar.vue'
import UserAvatar from '@/components/UserAvatar.vue'
import { ref, reactive, toRaw } from 'vue'
import { Message } from '@arco-design/web-vue'
import client from '@/utils/graphql-client'
import isMdScreen from '../uses/is-md-screen'
import { userThumbFrag } from '@/gql/user'

const doc = gql`
  query Users($page: Int, $size: Int, $name: String, $mobile: String, $level: Int) {
    users(page: $page, size: $size, name: $name, mobile: $mobile, level: $level) {
      total
      list {
        ...UserThumbFields
      }
    }
  }
  ${userThumbFrag}
`

const createUserMutation = gql`
  mutation CreateUser($markName: String!, $mobile: String!, $gender: Int, $level: Int) {
    createUser(markName: $markName, mobile: $mobile, gender: $gender, level: $level) {
      id
      ...UserThumbFields
    }
  }
  ${userThumbFrag}
`
const drawerVisible = ref(false)
const submitting = ref(false)
const userForm = reactive({
  markName: '',
  mobile: '',
  gender: 0,
  level: 0,
})

const openDrawer = () => {
  drawerVisible.value = true
}

const handleSubmit = async ({ errors }) => {
  submitting.value = true
  if (errors) {
    submitting.value = false
    return
  }
  try {
    await client(createUserMutation, toRaw(userForm))
    Message.success('添加用户成功成功')
    userForm.markName = ''
    userForm.mobile = ''
    userForm.gender = 0
    userForm.level = 0
    search()
  } catch (error) {
    Message.error(error.message)
  }
}

const { loaded, error, fetching, list, total, search, form, variables, goPage } =
  usePageRequest(doc)
</script>
