<template>
  <div class="mx-auto w-full max-w-md flex flex-col items-center justify-center h-screen">
    <div class="w-full">
      <div class="text-xl font-bold mb-8 px-4">你好，欢迎登录</div>
      <div class="bg-[var(--color-bg-1)] pt-4 pr-4 w-full">
        <a-form :model="formData" @submit="onSubmit">
          <a-form-item field="username" label="手机号">
            <a-input v-model="formData.username" placeholder="请输入手机号" />
          </a-form-item>
          <a-form-item field="password" label="密码">
            <a-input-password v-model="formData.password" placeholder="请输入密码" />
          </a-form-item>
          <a-form-item>
            <a-button html-type="submit" type="primary" long :loading="submitting">登录</a-button>
          </a-form-item>
        </a-form>
      </div>
    </div>
  </div>
</template>
<script setup>
import { reactive, ref } from 'vue'
import { useRouter } from 'vue-router'
import { Message } from '@arco-design/web-vue'
import { useAccount } from '@/uses/account.js'
const { login } = useAccount()
const router = useRouter()
const formData = reactive({ username: '', password: '' })
const submitting = ref(false)
const onSubmit = async () => {
  console.log(formData)
  login(formData.username, formData.password)
    .then(() => {
      Message.success('登录成功')
      router.push('/')
    })
    .catch((e) => {
      Message.error(e.message)
    })
}
</script>
