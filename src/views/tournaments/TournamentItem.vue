<template>
  <div v-if="tournament" class="pb-10">
    <div class="flex py-2">
      <div class="flex-1 flex items-center gap-2">
        <div class="flex items-center font-bold">
          <date-time :time="tournament.start" :show-time="false"></date-time>
          <span>~</span>
          <date-time :time="tournament.end" :show-time="false"></date-time>
          <a-button
            size="mini"
            type="text"
            @click="openTournamentDrawer"
            v-permission="['admin', 'executor']"
          >
            <template #icon><IconEdit /></template>编辑
          </a-button>
        </div>
        <div>
          <a-tag>{{ tournament.hall.name }}</a-tag>
        </div>
        <div>
          <a-tag v-if="tournament.status === 0" color="orange">未开始</a-tag>
          <a-tag v-else-if="tournament.status === 1" color="blue">进行中</a-tag>
          <a-tag v-else-if="tournament.status === 2" color="red">已结束</a-tag>
        </div>
      </div>
    </div>
    <!-- 排位对赛 -->
    <div class="rounded-md px-4 pt-2 pb-2 overflow-hidden bg-white mb-2">
      <div class="pb-1 border-b border-[var(--color-border-1)] flex items-center">
        <div class="font-bold text-base flex-1">排位对赛</div>
        <div v-if="tournament.status === 1">
          <a-button type="primary" shape="round" size="mini" @click="matchDrawerVisible = true">
            <template #icon><IconPlus /></template>对赛</a-button
          >
        </div>
      </div>
      <div class="pt-2 grid grid-cols-1 md:grid-cols-3 lg:grid-cols-4 gap-2">
        <div v-if="tournament.matches.length === 0" class="py-4 text-[var(--color-text-3)]">
          <div>还没有对赛，请添加</div>
        </div>
        <div
          v-for="match in tournament.matches"
          :key="match.id"
          class="rounded-md p-2 border border-[var(--color-border-1)]"
        >
          <a-descriptions :column="1" :align="{ label: 'right' }" size="small">
            <a-descriptions-item label="时间">
              <div class="flex">
                <div class="flex-1">
                  <date-time v-if="match.playedAt" show-time :time="match.playedAt"></date-time>
                  <div v-else class="text-[var(--color-text-3)]">--</div>
                </div>
                <div v-if="match.status === 0">
                  <a-button
                    v-permission="['admin', 'executor']"
                    type="primary"
                    size="mini"
                    shape="round"
                    @click="openApproveModal(match)"
                    ><template #icon><icon-stamp></icon-stamp></template>审核</a-button
                  >
                </div>
              </div>
            </a-descriptions-item>
            <a-descriptions-item label="状态">
              <a-tag v-if="match.status === 1" color="arcoblue">审核通过</a-tag>
              <a-tag v-else-if="match.status === 2" color="red">未审核通过</a-tag>
              <a-tag v-else-if="match.status === 0" color="orange">等待审核</a-tag>
            </a-descriptions-item>
            <a-descriptions-item label="选手1">
              <div class="flex items-center">
                <div class="flex-1"><user-avatar :user="match.user1"></user-avatar></div>
                <div class="text-right">
                  <div class="font-bold">{{ match.points1 }}</div>
                  <div class="text-xs text-[var(--color-text-3)]">积分</div>
                </div>
              </div>
            </a-descriptions-item>
            <a-descriptions-item label="选手2">
              <div class="flex items-center">
                <div class="flex-1"><user-avatar :user="match.user2"></user-avatar></div>
                <div class="text-right">
                  <div class="font-bold">{{ match.points2 }}</div>
                  <div class="text-xs text-[var(--color-text-3)]">积分</div>
                </div>
              </div>
            </a-descriptions-item>
            <a-descriptions-item label="备注">
              <div v-if="match.comment">{{ match.comment }}</div>
              <div v-else class="text-[var(--color-text-3)]">--</div>
            </a-descriptions-item>
            <a-descriptions-item label="图片">
              <a-image-preview-group infinite v-if="match.images.length">
                <div class="grid grid-cols-2 gap-1">
                  <a-image
                    class="cursor-pointer"
                    v-for="img in match.images"
                    :key="img"
                    :src="img + '-cover.jpg'"
                    width="100%"
                  />
                </div>
              </a-image-preview-group>
              <div v-else class="text-[var(--color-text-3)]">--</div>
            </a-descriptions-item>
            <a-descriptions-item label="提交">
              <div class="text-xs text-[var(--color-text-2)]">
                <span class="mr-3">{{ match.createdBy.name }}</span>
                <date-time :time="match.createdAt" show-time pretty></date-time>
              </div>
            </a-descriptions-item>
            <a-descriptions-item label="审核" v-if="match.status !== 0">
              <div class="text-xs text-[var(--color-text-2)]">
                <span class="mr-3">{{ match.approvedBy.name }}</span>
                <date-time :time="match.approvedAt" show-time pretty></date-time>
              </div>
              <div v-if="match.approveComment" class="text-sm">{{ match.approveComment }}</div>
            </a-descriptions-item>
          </a-descriptions>
        </div>
      </div>
    </div>
    <div class="grid grid-cols-1 md:grid-cols-2 gap-2">
      <!-- 排位积分 -->
      <div class="rounded-md px-4 pt-2 pb-2 overflow-hidden bg-white">
        <div class="pb-1 border-b border-[var(--color-border-1)] flex items-center">
          <div class="font-bold text-base flex-1">排位积分</div>
        </div>
        <div>
          <div class="px-2 flex font-semibold text-[var(--color-text-3)] pt-2 pb-2">
            <div class="pl-3">日期</div>
            <div class="flex-1 pl-12">客户</div>
            <div>积分</div>
          </div>
          <a-tabs position="left">
            <a-tab-pane v-for="item in userPoints" :title="item[0]" :key="item[0]">
              <div
                class="flex items-center py-1 px-2"
                :class="index % 2 === 0 ? 'bg-[var(--color-fill-1)]' : ''"
                v-for="(up, index) in item[1]"
                :key="up.id"
              >
                <div class="flex-1"><user-avatar :user="up.user"></user-avatar></div>
                <div class="font-bold">{{ up.points }}</div>
              </div>
            </a-tab-pane>
          </a-tabs>
        </div>
      </div>
      <!--  巅峰对决 -->
      <div class="rounded-md px-4 pt-2 pb-2 overflow-hidden bg-white">
        <div class="pb-1 border-b border-[var(--color-border-1)] flex items-center">
          <div class="font-bold text-base flex-1">巅峰对决</div>
          <div v-if="tournament.status === 1">
            <a-button type="primary" shape="round" size="mini" @click="openWinnersDrawer">
              <template #icon><IconUserGroup /></template>设置</a-button
            >
          </div>
        </div>
        <div>
          <div class="px-2 flex font-semibold text-[var(--color-text-3)] pt-2 pb-1">
            <div class="flex-1">客户</div>
            <div>排名</div>
          </div>
          <div
            class="flex items-center py-1 px-2"
            :class="index % 2 === 0 ? 'bg-[var(--color-fill-1)]' : ''"
            v-for="(w, index) in tournament.winners"
            :key="w.rank"
          >
            <div class="flex-1"><user-avatar :user="w.user"></user-avatar></div>
            <div class="font-bold">{{ w.rank }}</div>
          </div>
        </div>
      </div>
    </div>
  </div>
  <div v-else-if="error">
    <div>出错了，{{ error.message }}</div>
  </div>
  <!-- 编辑赛程 -->
  <a-drawer
    v-model:visible="tournamentDrawerVisible"
    title="编辑赛程"
    :width="500"
    :placement="isMdScreen ? 'right' : 'bottom'"
    height="90%"
    unmount-on-close
    :footer="false"
  >
    <a-form
      :model="tournamentForm"
      ref="formRef"
      @submit="handleTournamentSubmit"
      :label-col-props="{ span: 6 }"
      :wrapper-col-props="{ span: 14 }"
    >
      <a-form-item
        field="start"
        label="开始时间"
        :rules="[{ required: true, message: '请选择开始日期' }]"
      >
        <a-date-picker v-model="tournamentForm.start" />
      </a-form-item>
      <a-form-item
        field="end"
        label="结束时间"
        :rules="[{ required: true, message: '请选择结束日期' }]"
      >
        <a-date-picker v-model="tournamentForm.end" />
      </a-form-item>
      <a-form-item field="end" label="">
        <div class="flex flex-1 gap-2">
          <div>
            <a-popconfirm
              content="确定要结束赛程吗?"
              v-if="tournament.status === 1"
              @ok="updateTournament({ status: 2 })"
            >
              <a-button type="primary">结束赛程</a-button>
            </a-popconfirm>
            <a-popconfirm
              content="确定要开启赛程吗?"
              v-if="tournament.status === 2"
              @ok="updateTournament({ status: 1 })"
            >
              <a-button type="primary">开始赛程</a-button>
            </a-popconfirm>
          </div>
          <div class="flex-1">
            <a-button type="primary" html-type="submit" long
              ><template #icon><IconSave /></template>保存</a-button
            >
          </div>
        </div>
      </a-form-item>
    </a-form>
  </a-drawer>
  <!-- 添加对赛 -->
  <a-drawer
    v-model:visible="matchDrawerVisible"
    title="添加排位对赛"
    :width="500"
    :placement="isMdScreen ? 'right' : 'bottom'"
    height="90%"
    unmount-on-close
    :footer="false"
  >
    <a-form :model="matchForm" @submit="handleMatchSubmit">
      <a-form-item
        label="比赛时间"
        field="playedAt"
        :rules="[{ required: true, message: '请输入比赛时间' }]"
      >
        <a-date-picker show-time v-model="matchForm.playedAt" format="YYYY-MM-DD HH:mm" />
      </a-form-item>
      <a-form-item
        label="选手1"
        field="user1Id"
        :rules="[{ required: true, message: '请选择参赛选手' }]"
      >
        <user-selector v-model="matchForm.user1Id" />
      </a-form-item>
      <a-form-item
        label="选手1积分"
        field="points1"
        :rules="[{ required: true, message: '请输入积分' }]"
      >
        <a-input-number v-model="matchForm.points1"></a-input-number>
      </a-form-item>
      <a-form-item
        label="选手2"
        field="user2Id"
        :rules="[{ required: true, message: '请选择参赛选手' }]"
      >
        <user-selector v-model="matchForm.user2Id" />
      </a-form-item>
      <a-form-item
        label="选手2积分"
        field="points2"
        :rules="[{ required: true, message: '请输入积分' }]"
      >
        <a-input-number v-model="matchForm.points2"></a-input-number>
      </a-form-item>
      <multiple-img-uploader
        label="图片"
        prefix="matches"
        suffix="-cover.jpg"
        :required="false"
        v-model="matchForm.images"
      />
      <a-form-item field="comment" label="备注">
        <a-textarea
          v-model="matchForm.comment"
          placeholder="备注说明"
          :max-length="200"
          show-word-limit
        />
      </a-form-item>
      <a-form-item>
        <a-button type="primary" long html-type="submit">提交</a-button>
      </a-form-item>
    </a-form>
  </a-drawer>
  <!-- 审核 -->
  <a-modal
    v-model:visible="approvalModalVisible"
    title="审核"
    width="500px"
    :modal-style="{ maxWidth: '100vw' }"
    :footer="false"
  >
    <a-form :model="approvalForm" layout="vertical" class="mt-2">
      <a-form-item field="approveComment" label="审核备注">
        <a-textarea
          v-model="approvalForm.approveComment"
          placeholder="请输入审核备注..."
          :max-length="200"
          show-word-limit
          :rows="4"
        />
      </a-form-item>

      <!-- 操作按钮 -->
      <div class="flex gap-3 justify-end">
        <a-button @click="approvalModalVisible = false">取消</a-button>
        <a-button status="danger" @click="handleApproval(2)" :loading="approvalSubmitting">
          不通过
        </a-button>
        <a-button type="primary" @click="handleApproval(1)" :loading="approvalSubmitting">
          通过
        </a-button>
      </div>
    </a-form>
  </a-modal>
  <a-drawer
    v-model:visible="winnersDrawerVisible"
    title="设置巅峰赛排名"
    :width="500"
    :placement="isMdScreen ? 'right' : 'bottom'"
    height="90%"
    unmount-on-close
    ok-text="保存"
    @before-ok="saveWinners"
  >
    <user-selector @selected="addWinner" :show-user="false" label="添加获胜客户"></user-selector>
    <div>
      <div class="flex font-semibold text-[var(--color-text-3)] pb-1 pt-1">
        <div class="flex-1">客户</div>
        <div class="pr-6">排名</div>
      </div>
      <draggable v-model="winners" @end="moveWinnerEnd" item-key="user.id" ghost-class="ghost">
        <template #item="{ element, index }">
          <div
            class="bg-white flex items-center border border-[var(--color-border-1)] rounded-md py-1 px-4 gap-x-3 mb-2"
          >
            <div class="flex-1">
              <user-avatar :user="element.user"></user-avatar>
            </div>
            <div class="font-bold">{{ element.rank }}</div>
            <div>
              <a-button type="text" @click="deleteWinner(index)"
                ><template #icon><IconDelete></IconDelete></template
              ></a-button>
              <a-button type="text">
                <template #icon><IconSwap class="rotate-90"></IconSwap></template>
              </a-button>
            </div>
          </div>
        </template>
      </draggable>
    </div>
  </a-drawer>
</template>
<script setup>
import gql from 'graphql-tag'
import { useRequest } from '@/uses/request'
import { userShortFrag } from '@/gql/user'
import { watchEffect, ref, reactive, computed } from 'vue'
import { dayjs } from '@arco-design/web-vue/es/_utils/date'
import isMdScreen from '@/uses/is-md-screen'
import client from '@/utils/graphql-client'
import UserSelector from '@/components/UserSelector.vue'
import multipleImgUploader from '@/components/uploader/multiple-img-uploader.vue'
import { Message } from '@arco-design/web-vue'
import UserAvatar from '@/components/UserAvatar.vue'
import draggable from 'vuedraggable'
const props = defineProps({
  id: String,
})
const frag = gql`
  fragment TournamentFields on Tournament {
    id
    start
    end
    status
    hall {
      id
      name
    }
    winners {
      user {
        ...UserFields
      }
      rank
    }
    userPoints {
      id
      user {
        ...UserFields
      }
      points
      playDate
    }
    matches {
      id
      user1 {
        ...UserFields
      }
      points1
      user2 {
        ...UserFields
      }
      points2
      status
      playedAt
      comment
      images
      createdAt
      createdBy {
        id
        name
      }
      approvedAt
      approvedBy {
        id
        name
      }
      approveComment
    }
  }
  ${userShortFrag}
`
const doc = gql`
  query Query($tournamentId: ID) {
    tournament(id: $tournamentId) {
      ...TournamentFields
    }
  }
  ${frag}
`
const {
  error,
  data: { tournament },
  variables,
} = useRequest(doc, { lazy: true })
watchEffect(() => {
  if (props.id) {
    variables.tournamentId = props.id
  }
})
const userPoints = computed(() => {
  if (!tournament.value) return null
  const userPoints = tournament.value.userPoints
  const result = {}
  for (let i = 0; i < userPoints.length; i++) {
    const item = userPoints[i]
    const playDate = item.playDate <= 0 ? '所有' : dayjs(item.playDate).format('MM/DD')
    result[playDate] = result[playDate] || []
    result[playDate].push(item)
  }
  return Object.entries(result)
})
// 编辑 tournament
const tournamentDrawerVisible = ref(false)
const tournamentForm = reactive({
  start: '',
  end: '',
})
function openTournamentDrawer() {
  tournamentForm.start = tournament.value.start
  tournamentForm.end = tournament.value.end
  tournamentDrawerVisible.value = true
}
async function handleTournamentSubmit({ errors }) {
  if (errors) return
  await updateTournament({ ...tournamentForm })
}
async function updateTournament(data) {
  const doc = gql`
    mutation UpdateTournament($id: ID!, $start: DateTime, $end: DateTime, $status: Int) {
      updateTournament(id: $id, start: $start, end: $end, status: $status) {
        ...TournamentFields
      }
    }
    ${frag}
  `
  try {
    const res = await client(doc, { id: props.id, ...data })
    tournamentDrawerVisible.value = false
    tournament.value = res.updateTournament
    Message.success('保存成功')
  } catch (err) {
    Message.error(err.message)
  } finally {
    // submitting.value = false
  }
}
// 添加比赛 drawer
const matchDrawerVisible = ref(false)
const matchForm = reactive({
  playedAt: '',
  user1Id: '',
  points1: null,
  user2Id: '',
  points2: null,
  comment: '',
  images: [],
})
async function handleMatchSubmit({ errors }) {
  console.log(errors)
  if (errors) return
  const doc = gql`
    mutation Mutation(
      $tournamentId: ID!
      $user1Id: ID!
      $points1: Int!
      $user2Id: ID!
      $points2: Int!
      $comment: String
      $playedAt: String!
      $images: [String]
    ) {
      addTournamentMatch(
        tournamentId: $tournamentId
        user1Id: $user1Id
        points1: $points1
        user2Id: $user2Id
        points2: $points2
        comment: $comment
        playedAt: $playedAt
        images: $images
      ) {
        ...TournamentFields
      }
    }
    ${frag}
  `
  try {
    const res = await client(doc, { tournamentId: tournament.value.id, ...matchForm })
    tournament.value = res.addTournamentMatch
    matchForm.user1Id = ''
    matchForm.points1 = null
    matchForm.user2Id = ''
    matchForm.points2 = null
    matchForm.comment = ''
    matchForm.playedAt = ''
    matchForm.images = []
    matchDrawerVisible.value = false
  } catch (err) {
    Message.error(err.message)
  } finally {
    // submitting.value = false
  }
}
// 审核相关状态
const approvalModalVisible = ref(false)
const currentMatch = ref(null)
const approvalSubmitting = ref(false)
const approvalForm = reactive({
  approveComment: '',
})
const openApproveModal = (match) => {
  currentMatch.value = match
  approvalModalVisible.value = true
}
// 处理审核操作
const handleApproval = async (status) => {
  approvalSubmitting.value = true
  try {
    if (status === 2 && !approvalForm.approveComment.trim()) {
      Message.warning('请输入审核备注')
      return
    }
    const mutation = gql`
      mutation ApproveTournamentMatch($id: ID!, $status: Int, $approveComment: String) {
        approveTournamentMatch(id: $id, status: $status, approveComment: $approveComment) {
          ...TournamentFields
        }
      }
      ${frag}
    `

    const res = await client(mutation, {
      id: currentMatch.value.id,
      status,
      approveComment: approvalForm.approveComment,
    })
    tournament.value = res.approveTournamentMatch

    Message.success(
      status === 1
        ? '审核通过'
        : status === 2
          ? '审核不通过'
          : status === 0
            ? '已重置未未审核'
            : '未定义',
    )
    approvalForm.approveComment = ''
    approvalModalVisible.value = false
  } catch (error) {
    Message.error(error.message || '审核失败')
  } finally {
    approvalSubmitting.value = false
  }
}
// 设置 winner
const winnersDrawerVisible = ref(false)
const winners = ref([])
function openWinnersDrawer() {
  winnersDrawerVisible.value = true
  winners.value = tournament.value.winners
}
function addWinner(u) {
  if (winners.value.some((i) => i.user.id === u.id)) {
    Message.warning('不能重复添加客户')
    return
  }
  winners.value.push({ rank: winners.value.length + 1, user: u })
}
function moveWinnerEnd() {
  winners.value = winners.value.map((i, index) => ({ ...i, rank: index + 1 }))
}
function deleteWinner(index) {
  winners.value = [...winners.value.slice(0, index), ...winners.value.slice(index + 1)].map(
    (i, index) => ({ ...i, rank: index + 1 }),
  )
}
async function saveWinners(done) {
  if (winners.value.length === 0) {
    Message.warning('排名为空')
    done(false)
    return
  }
  const data = {
    tournamentId: props.id,
    winners: winners.value.map((i) => ({ rank: i.rank, userId: i.user.id })),
  }
  try {
    const mutation = gql`
      mutation UpdateTournamentWinner($tournamentId: ID!, $winners: [TournamentWinnerInput!]) {
        updateTournamentWinner(tournamentId: $tournamentId, winners: $winners) {
          ...TournamentFields
        }
      }
      ${frag}
    `
    const res = await client(mutation, data)
    tournament.value = res.updateTournamentWinner
    Message.success('保存成功')
  } catch (error) {
    Message.error(error.message || '审核失败')
  }
}
</script>
<style>
.ghost {
  background-color: bisque;
}
</style>
