<template>
  <page-loading :loaded="loaded" :error="error">
    <div class="bg-[var(--color-bg-2)] p-2 rounded-md mb-2">
      <div>
        <div class="mb-2 flex gap-x-2 items-center">
          <a-radio-group type="button" :default-value="variables.hallId" @change="searchList">
            <a-radio :value="hall.id" v-for="hall in account.halls" :key="hall.id">
              {{ hall.name }}
            </a-radio>
          </a-radio-group>
          <a-button type="primary" @click="drawerVisible = true" size="small">
            <template #icon><IconPlus /> </template>
            新赛程
          </a-button>
        </div>
        <div class="flex">
          <div class="flex-1 flex flex-nowrap gap-2 overflow-scroll">
            <div v-for="item in list" :key="item.id">
              <a-tag
                class="cursor-pointer"
                @click="selectedTournament = item"
                :color="selectedTournament && selectedTournament.id === item.id ? 'arcoblue' : null"
              >
                <date-time :time="item.start" :show-time="false"></date-time>
                <span>~</span>
                <date-time :time="item.end" :show-time="false"></date-time>
                <template #icon v-if="selectedTournament && selectedTournament.id === item.id">
                  <icon-check-circle-fill />
                </template>
              </a-tag>
            </div>
          </div>
          <a-pagination
            size="mini"
            simple
            :total="total"
            :page-size="variables.size"
            @change="(page) => goPage(page, variables.size)"
            @page-size-change="(size) => goPage(1, size)"
          ></a-pagination>
        </div>
      </div>
    </div>

    <tournament-item :id="selectedTournament ? selectedTournament.id : null" />
  </page-loading>
  <a-drawer
    v-model:visible="drawerVisible"
    title="开启新赛程"
    :width="500"
    :placement="isMdScreen ? 'right' : 'bottom'"
    height="90%"
    unmount-on-close
    :footer="false"
  >
    <a-form
      :model="form"
      ref="formRef"
      @submit="handleSubmit"
      :label-col-props="{ span: 6 }"
      :wrapper-col-props="{ span: 14 }"
    >
      <a-form-item field="hallId" label="门店" :rules="[{ required: true, message: '请选择门店' }]">
        <a-select v-model="form.hallId">
          <a-option v-for="hall in account.halls" :key="hall.id" :value="hall.id">
            {{ hall.name }}
          </a-option>
        </a-select>
      </a-form-item>
      <a-form-item
        field="start"
        label="开始时间"
        :rules="[{ required: true, message: '请选择开始日期' }]"
      >
        <a-date-picker v-model="form.start" />
      </a-form-item>
      <a-form-item
        field="end"
        label="结束时间"
        :rules="[{ required: true, message: '请选择结束日期' }]"
      >
        <a-date-picker v-model="form.end" />
      </a-form-item>
      <a-form-item field="end" label="">
        <a-button type="primary" html-type="submit" block long v-permission="['admin', 'executor']"
          >创建</a-button
        >
      </a-form-item>
    </a-form>
  </a-drawer>
</template>
<script setup>
import gql from 'graphql-tag'
import { ref, reactive, toRaw, watch } from 'vue'
import { usePageRequest } from '@/uses/page-request'
import PageLoading from '@/components/PageLoading.vue'
import DateTime from '@/components/DateTime.vue'
import { Message } from '@arco-design/web-vue'
import client from '@/utils/graphql-client'
import isMdScreen from '@/uses/is-md-screen'
import { useAccount } from '@/uses/account'
import TournamentItem from './TournamentItem.vue'
const { account } = useAccount()
const doc = gql`
  query Tournaments($hallId: ID!, $page: Int, $size: Int = 10) {
    tournaments(hallId: $hallId, page: $page, size: $size) {
      total
      list {
        id
        start
        end
        status
      }
    }
  }
`
const {
  loaded,
  error,
  list,
  total,
  search,
  form: listForm,
  variables,
  goPage,
} = usePageRequest(doc, { lazy: true })
listForm.hallId = account.value.halls[0].id
search()
function searchList(v) {
  listForm.hallId = v
  search()
}
const drawerVisible = ref(false)
const form = reactive({
  hallId: account.value.halls[0].id,
  start: '',
  end: '',
})
const selectedTournament = ref(null)
watch(
  () => list.value,
  (v) => {
    selectedTournament.value = v.length ? v[0] : null
  },
)

const handleSubmit = async ({ errors }) => {
  if (errors) {
    return
  }
  const doc = gql`
    mutation Mutation($hallId: ID!, $start: DateTime!, $end: DateTime!) {
      addTournament(hallId: $hallId, start: $start, end: $end) {
        id
      }
    }
  `
  try {
    await client(doc, toRaw(form))
    Message.success('创建成功')
    form.start = ''
    form.end = ''
    drawerVisible.value = false
    search()
  } catch (err) {
    Message.error(err.message || '创建失败')
  }
}
</script>
